// ==UserScript==
// @name         世界书优化器 (World Info Optimizer)
// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0
// @match        */*
// @version      1.0.0
// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。
// <AUTHOR> & AI Assistant
// @grant        none
// @inject-into  content
// ==/UserScript==

'use strict';

// 使用IIFE封装，避免全局污染
(() => {
  console.log('[WorldInfoOptimizer] Script execution started.');

  // --- 类型定义 ---
  interface LorebookEntry {
    uid: string;
    comment: string;
    content: string;
    keys: string[];
    enabled: boolean;
    display_index: number;
    [key: string]: any;
  }

  interface AppState {
    regexes: {
      global: any[];
      character: any[];
    };
    lorebooks: {
      character: string[];
    };
    chatLorebook: string | null;
    allLorebooks: Array<{ name: string; enabled: boolean }>;
    lorebookEntries: Map<string, LorebookEntry[]>;
    lorebookUsage: Map<string, string[]>;
    activeTab: string;
    isDataLoaded: boolean;
    searchFilters: {
      bookName: boolean;
      entryName: boolean;
      keywords: boolean;
      content: boolean;
    };
    multiSelectMode: boolean;
    selectedItems: Set<string>;
  }

  // --- 配置常量 ---
  const SCRIPT_VERSION_TAG = 'v1_0_0';
  const PANEL_ID = 'world-info-optimizer-panel';
  const BUTTON_ID = 'world-info-optimizer-button';
  const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
  const BUTTON_TOOLTIP = '世界书优化器';
  const BUTTON_TEXT_IN_MENU = '世界书优化器';
  const SEARCH_INPUT_ID = 'wio-search-input';
  const REFRESH_BTN_ID = 'wio-refresh-btn';
  const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
  const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
  const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';

  const LOREBOOK_OPTIONS = {
    position: {
      before_character_definition: '角色定义前',
      after_character_definition: '角色定义后',
      before_example_messages: '聊天示例前',
      after_example_messages: '聊天示例后',
      before_author_note: '作者笔记前',
      after_author_note: '作者笔记后',
      at_depth_as_system: '@D ⚙ 系统',
      at_depth_as_assistant: '@D 🗨️ 角色',
      at_depth_as_user: '@D 👤 用户',
    },
    logic: {
      and_any: '任一 AND',
      and_all: '所有 AND',
      not_any: '任一 NOT',
      not_all: '所有 NOT',
    },
  };

  // --- 应用程序状态 ---
  const appState: AppState = {
    regexes: { global: [], character: [] },
    lorebooks: { character: [] },
    chatLorebook: null,
    allLorebooks: [],
    lorebookEntries: new Map(),
    lorebookUsage: new Map(),
    activeTab: 'global-lore',
    isDataLoaded: false,
    searchFilters: { bookName: true, entryName: true, keywords: true, content: true },
    multiSelectMode: false,
    selectedItems: new Set(),
  };

  // --- 全局变量 ---
  let parentWin: any;
  let $: any;
  let TavernHelper: any;

  /**
   * 等待DOM和API就绪
   */
  function onReady(callback: (jquery: any, tavernHelper: any) => void): void {
    const domSelector = '#extensionsMenu';
    const maxRetries = 100;
    let retries = 0;

    console.log(
      `[WorldInfoOptimizer] Starting readiness check. Polling for DOM element "${domSelector}" AND core APIs.`,
    );

    const interval = setInterval(() => {
      const parentDoc = window.parent.document;
      parentWin = window.parent;

      const domReady = parentDoc.querySelector(domSelector) !== null;
      const apiReady =
        parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;

      if (domReady && apiReady) {
        clearInterval(interval);
        console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);
        try {
          callback(parentWin.jQuery, parentWin.TavernHelper);
        } catch (e) {
          console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);
        }
      } else {
        retries++;
        if (retries > maxRetries) {
          clearInterval(interval);
          console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);
          if (!domReady) console.error(`[WorldInfoOptimizer] -> Failure: DOM element "${domSelector}" not found.`);
          if (!apiReady) console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);
        }
      }
    }, 150);
  }

  /**
   * 错误处理包装器
   */
  const errorCatched =
    (fn: Function, context = 'WorldInfoOptimizer') =>
    async (...args: any[]) => {
      try {
        return await fn(...args);
      } catch (error) {
        if (error) {
          console.error(`[${context}] Error:`, error);
          await showModal({
            type: 'alert',
            title: '脚本异常',
            text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,
          });
        }
      }
    };

  // --- 安全访问 lorebookEntries 的函数 ---
  const safeGetLorebookEntries = (bookName: string): LorebookEntry[] => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      if (typeof appState.lorebookEntries.get !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      const entries = appState.lorebookEntries.get(bookName);
      return Array.isArray(entries) ? entries : [];
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      return [];
    }
  };

  const safeSetLorebookEntries = (bookName: string, entries: LorebookEntry[]): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      if (typeof appState.lorebookEntries.set !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    }
  };

  const safeDeleteLorebookEntries = (bookName: string): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      if (typeof appState.lorebookEntries.delete !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      appState.lorebookEntries.delete(bookName);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);
      appState.lorebookEntries = new Map();
    }
  };

  const safeClearLorebookEntries = (): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      if (typeof appState.lorebookEntries.clear !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      appState.lorebookEntries.clear();
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);
      appState.lorebookEntries = new Map();
    }
  };

  const safeHasLorebookEntries = (bookName: string): boolean => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return false;
      }

      if (typeof appState.lorebookEntries.has !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return false;
      }

      return appState.lorebookEntries.has(bookName);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      return false;
    }
  };

  // --- 工具函数 ---
  const escapeHtml = (text: any): string => {
    if (typeof text !== 'string') return String(text);
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const highlightText = (text: string, searchTerm: string): string => {
    if (!searchTerm || !text) return escapeHtml(text);

    const escapedText = escapeHtml(text);
    const htmlSafeSearchTerm = escapeHtml(searchTerm);
    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');

    return escapedText.replace(regex, '<mark class="wio-highlight">$1</mark>');
  };

  // --- 通知和模态框函数 ---
  const showSuccessTick = (message = '操作成功', duration = 1500): void => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0) return;

    $panel.find('.wio-toast-notification').remove();

    const toastHtml = `<div class="wio-toast-notification"><i class="fa-solid fa-check-circle"></i> ${escapeHtml(message)}</div>`;
    const $toast = $(toastHtml);

    $panel.append($toast);

    setTimeout(() => {
      $toast.addClass('visible');
    }, 10);

    setTimeout(() => {
      $toast.removeClass('visible');
      setTimeout(() => {
        $toast.remove();
      }, 300);
    }, duration);
  };

  const showProgressToast = (initialMessage = '正在处理...') => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0) return { update: () => {}, remove: () => {} };

    $panel.find('.wio-progress-toast').remove();

    const toastHtml = `<div class="wio-progress-toast"><i class="fa-solid fa-spinner fa-spin"></i> <span class="wio-progress-text">${escapeHtml(initialMessage)}</span></div>`;
    const $toast = $(toastHtml);

    $panel.append($toast);

    setTimeout(() => {
      $toast.addClass('visible');
    }, 10);

    const update = (newMessage: string) => {
      $toast.find('.wio-progress-text').html(escapeHtml(newMessage));
    };

    const remove = () => {
      $toast.removeClass('visible');
      setTimeout(() => {
        $toast.remove();
      }, 300);
    };

    return { update, remove };
  };

  interface ModalOptions {
    type?: 'alert' | 'confirm' | 'prompt';
    title?: string;
    text?: string;
    placeholder?: string;
    value?: string;
  }

  const showModal = (options: ModalOptions): Promise<any> => {
    return new Promise((resolve, reject) => {
      const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;
      let buttonsHtml = '';
      if (type === 'alert') buttonsHtml = '<button class="wio-modal-btn wio-modal-ok">确定</button>';
      else if (type === 'confirm')
        buttonsHtml =
          '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确认</button>';
      else if (type === 'prompt')
        buttonsHtml =
          '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';

      const inputHtml =
        type === 'prompt'
          ? `<input type="text" class="wio-modal-input" placeholder="${escapeHtml(placeholder)}" value="${escapeHtml(value)}">`
          : '';

      const modalHtml = `<div class="wio-modal-overlay"><div class="wio-modal-content"><div class="wio-modal-header">${escapeHtml(title)}</div><div class="wio-modal-body"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class="wio-modal-footer">${buttonsHtml}</div></div></div>`;

      const $modal = $(modalHtml).hide();
      const $panel = $(`#${PANEL_ID}`, parentWin.document);
      if ($panel.length > 0) {
        $panel.append($modal);
      } else {
        $('body', parentWin.document).append($modal);
      }

      $modal.fadeIn(200);
      const $input = $modal.find('.wio-modal-input');
      if (type === 'prompt') $input.focus().select();

      const closeModal = (isSuccess: boolean, val?: any) => {
        $modal.fadeOut(200, () => {
          $modal.remove();
          if (isSuccess) resolve(val);
          else reject();
        });
      };

      $modal.on('click', '.wio-modal-ok', () => {
        const val = type === 'prompt' ? $input.val() : true;
        if (type === 'prompt' && !String(val).trim()) {
          $input.addClass('wio-input-error');
          setTimeout(() => $input.removeClass('wio-input-error'), 500);
          return;
        }
        closeModal(true, val);
      });
      $modal.on('click', '.wio-modal-cancel', () => closeModal(false));
      if (type === 'prompt') {
        $input.on('keydown', (e: any) => {
          if (e.key === 'Enter') $modal.find('.wio-modal-ok').click();
          else if (e.key === 'Escape') closeModal(false);
        });
      }
    });
  };

  // --- API 包装器 ---
  const TavernAPI = {
    createLorebook: errorCatched(async (name: string) => await TavernHelper.createLorebook(name)),
    deleteLorebook: errorCatched(async (name: string) => await TavernHelper.deleteLorebook(name)),
    getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),
    setLorebookSettings: errorCatched(async (settings: any) => await TavernHelper.setLorebookSettings(settings)),
    getCharData: errorCatched(async () => await TavernHelper.getCharData()),
    Character: TavernHelper.Character,
    getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),
    replaceRegexes: errorCatched(
      async (regexes: any) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' }),
    ),
    getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),
    getCharLorebooks: errorCatched(async (charData?: any) => await TavernHelper.getCharLorebooks(charData)),
    getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),
    getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),
    getOrCreateChatLorebook: errorCatched(async (name: string) => await TavernHelper.getOrCreateChatLorebook(name)),
    setChatLorebook: errorCatched(async (name: string) => await TavernHelper.setChatLorebook(name)),
    getLorebookEntries: errorCatched(async (name: string) => await TavernHelper.getLorebookEntries(name)),
    setLorebookEntries: errorCatched(
      async (name: string, entries: LorebookEntry[]) => await TavernHelper.setLorebookEntries(name, entries),
    ),
    createLorebookEntries: errorCatched(
      async (name: string, entries: LorebookEntry[]) => await TavernHelper.createLorebookEntries(name, entries),
    ),
    deleteLorebookEntries: errorCatched(
      async (name: string, uids: string[]) => await TavernHelper.deleteLorebookEntries(name, uids),
    ),
    saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),
    setCurrentCharLorebooks: errorCatched(
      async (lorebooks: any) => await TavernHelper.setCurrentCharLorebooks(lorebooks),
    ),
  };

  // --- 数据加载函数 ---
  const loadAllData = errorCatched(async () => {
    const $content = $(`#${PANEL_ID}-content`, parentWin.document);
    $content.html('<p class="wio-info-text">正在加载所有数据，请稍候...</p>');

    try {
      // 防御性检查：确保SillyTavern API可用
      if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
        console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
        appState.regexes.global = [];
        appState.regexes.character = [];
        appState.allLorebooks = [];
        appState.lorebooks.character = [];
        appState.chatLorebook = null;
        safeClearLorebookEntries();
        appState.isDataLoaded = true;
        renderContent();
        return;
      }

      const context = parentWin.SillyTavern.getContext() || {};
      const allCharacters = Array.isArray(context.characters) ? context.characters : [];
      const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
      const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

      let charData = null,
        charLinkedBooks = null,
        chatLorebook = null;

      // 使用Promise.allSettled来避免单个失败影响整体
      const promises = [
        TavernAPI.getRegexes().catch(() => []),
        TavernAPI.getLorebookSettings().catch(() => ({})),
        TavernAPI.getLorebooks().catch(() => []),
      ];

      if (hasActiveCharacter) {
        promises.push(TavernAPI.getCharData().catch(() => null));
        promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));
      } else {
        promises.push(Promise.resolve(null), Promise.resolve(null));
      }

      if (hasActiveChat) {
        promises.push(
          TavernAPI.getChatLorebook().catch(error => {
            console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);
            return null;
          }),
        );
      } else {
        promises.push(Promise.resolve(null));
      }

      const results = await Promise.allSettled(promises);

      // 安全提取结果
      const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
      const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
      const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
      charData = results[3]?.status === 'fulfilled' ? results[3].value : null;
      charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;
      chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;

      appState.regexes.global = Array.isArray(allUIRegexes)
        ? allUIRegexes.filter((r: any) => r.scope === 'global')
        : [];
      updateCharacterRegexes(allUIRegexes, charData);

      safeClearLorebookEntries();
      appState.lorebookUsage.clear();
      const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);

      // 安全处理角色世界书
      if (Array.isArray(allCharacters) && allCharacters.length > 0) {
        try {
          await Promise.all(
            allCharacters.map(async (char: any) => {
              if (!char || !char.name) return;
              try {
                let books = null;
                try {
                  const result = TavernHelper.getCharLorebooks({ name: char.name });
                  if (result && typeof result.then === 'function') {
                    books = await result;
                  } else {
                    books = result;
                  }
                } catch (error) {
                  console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character "${char.name}":`, error);
                  books = null;
                }
                if (books && typeof books === 'object') {
                  const bookSet = new Set();
                  if (books.primary && typeof books.primary === 'string') bookSet.add(books.primary);
                  if (Array.isArray(books.additional)) {
                    books.additional.forEach((b: string) => typeof b === 'string' && bookSet.add(b));
                  }

                  bookSet.forEach(bookName => {
                    if (typeof bookName === 'string') {
                      if (!appState.lorebookUsage.has(bookName)) {
                        appState.lorebookUsage.set(bookName, []);
                      }
                      appState.lorebookUsage.get(bookName)!.push(char.name);
                      knownBookNames.add(bookName);
                      console.log(`[WorldInfoOptimizer] Character "${char.name}" uses lorebook "${bookName}"`);
                    }
                  });
                }
              } catch (charError) {
                console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);
              }
            }),
          );
        } catch (charProcessingError) {
          console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);
        }
      }

      const enabledGlobalBooks = new Set(
        Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : [],
      );
      appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name: string) => ({
        name: name,
        enabled: enabledGlobalBooks.has(name),
      }));

      const charBookSet = new Set();
      if (charLinkedBooks && typeof charLinkedBooks === 'object') {
        if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {
          charBookSet.add(charLinkedBooks.primary);
        }
        if (Array.isArray(charLinkedBooks.additional)) {
          charLinkedBooks.additional.forEach((name: string) => typeof name === 'string' && charBookSet.add(name));
        }
      }
      appState.lorebooks.character = Array.from(charBookSet);
      appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;
      if (typeof chatLorebook === 'string') {
        knownBookNames.add(chatLorebook);
      }

      const allBooksToLoad = Array.from(knownBookNames);
      const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);

      // 分批加载世界书条目，避免同时加载过多
      const batchSize = 5;
      for (let i = 0; i < allBooksToLoad.length; i += batchSize) {
        const batch = allBooksToLoad.slice(i, i + batchSize);
        await Promise.allSettled(
          batch.map(async name => {
            if (existingBookFiles.has(name) && typeof name === 'string') {
              try {
                const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);
                safeSetLorebookEntries(name, entries);
              } catch (entryError) {
                console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);
              }
            }
          }),
        );
      }

      appState.isDataLoaded = true;
      renderContent();
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in loadAllData:', error);
      $content.html(`
                <div style="padding: 20px; text-align: center;">
                    <p style="color: #ff6b6b; margin-bottom: 10px;">
                        <i class="fa-solid fa-exclamation-triangle"></i> 数据加载失败
                    </p>
                    <p style="color: #666; font-size: 14px;">
                        请检查开发者控制台获取详细信息，或尝试刷新页面。
                    </p>
                    <button class="wio-modal-btn" onclick="$('#${REFRESH_BTN_ID}').click()"
                            style="margin-top: 15px; padding: 8px 16px;">
                        <i class="fa-solid fa-refresh"></i> 重试
                    </button>
                </div>
            `);
      throw error;
    }
  });

  // --- 角色正则和世界书更新函数 ---
  function updateCharacterRegexes(allUIRegexes: any[], charData: any): void {
    const characterUIRegexes = allUIRegexes?.filter((r: any) => r.scope === 'character') || [];
    let cardRegexes: any[] = [];
    if (charData && TavernAPI.Character) {
      try {
        const character = new TavernAPI.Character(charData);
        cardRegexes = (character.getRegexScripts() || []).map((r: any, i: number) => ({
          id: r.id || `card-${Date.now()}-${i}`,
          script_name: r.scriptName || '未命名卡内正则',
          find_regex: r.findRegex,
          replace_string: r.replaceString,
          enabled: !r.disabled,
          scope: 'character',
          source: 'card',
        }));
      } catch (e) {
        console.warn('无法解析角色卡正则脚本:', e);
      }
    }
    const uiRegexIdentifiers = new Set(
      characterUIRegexes.map((r: any) => `${r.script_name}::${r.find_regex}::${r.replace_string}`),
    );
    const uniqueCardRegexes = cardRegexes.filter((r: any) => {
      const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
      return !uiRegexIdentifiers.has(identifier);
    });
    appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];
  }

  function updateCharacterLorebooks(charBooks: any): void {
    const characterBookNames: string[] = [];
    if (charBooks) {
      if (charBooks.primary) characterBookNames.push(charBooks.primary);
      if (charBooks.additional) characterBookNames.push(...charBooks.additional);
    }
    appState.lorebooks.character = [...new Set(characterBookNames)];
  }

  // --- 渲染函数 ---
  const renderContent = (): void => {
    const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';
    appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');
    appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');
    appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');
    appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');

    const $content = $(`#${PANEL_ID}-content`, parentWin.document);
    $content.empty();

    $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);
    const isLoreTab =
      appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';
    $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);

    updateSelectionCount();

    switch (appState.activeTab) {
      case 'global-lore':
        renderGlobalLorebookView(searchTerm, $content);
        break;
      case 'char-lore':
        renderCharacterLorebookView(searchTerm, $content);
        break;
      case 'chat-lore':
        renderChatLorebookView(searchTerm, $content);
        break;
      case 'global-regex':
        renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');
        break;
      case 'char-regex':
        renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');
        break;
    }
  };

  // --- 占位符函数（需要实现） ---
  const updateSelectionCount = (): void => {
    // TODO: 实现选择计数更新
  };

  const renderGlobalLorebookView = (searchTerm: string, $container: any): void => {
    const books = [...appState.allLorebooks].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name),
    );
    let filteredBookData: any[] = [];

    if (!searchTerm) {
      filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));
    } else {
      books.forEach(book => {
        const entries = [...safeGetLorebookEntries(book.name)];
        const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);
        const matchingEntries = entries.filter(
          entry =>
            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||
            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||
            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)),
        );

        if (bookNameMatches || matchingEntries.length > 0) {
          filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });
        }
      });
    }

    if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {
      $container.html(`<p class="wio-info-text">未找到匹配的世界书。</p>`);
    } else if (appState.allLorebooks.length === 0) {
      $container.html(`<p class="wio-info-text">还没有世界书，点击上方"+"创建一个吧。</p>`);
    }

    filteredBookData.forEach(data => {
      if (data && data.book) {
        $container.append(
          createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries),
        );
      }
    });
  };

  const renderCharacterLorebookView = (searchTerm: string, $container: any): void => {
    // TODO: 实现角色世界书视图渲染
    $container.html(`<p class="wio-info-text">角色世界书视图开发中...</p>`);
  };

  const renderChatLorebookView = (searchTerm: string, $container: any): void => {
    // TODO: 实现聊天世界书视图渲染
    $container.html(`<p class="wio-info-text">聊天世界书视图开发中...</p>`);
  };

  const renderRegexView = (regexes: any[], searchTerm: string, $container: any, title: string): void => {
    // TODO: 实现正则视图渲染
    $container.html(`<p class="wio-info-text">${title}视图开发中...</p>`);
  };

  const createGlobalLorebookElement = (
    book: any,
    searchTerm: string,
    forceShowAllEntries: boolean,
    filteredEntries: any[],
  ): any => {
    // TODO: 实现全局世界书元素创建
    return $(`<div class="wio-book-item"><h3>${escapeHtml(book.name)}</h3><p>开发中...</p></div>`);
  };

  // --- 主程序逻辑 ---
  function main(jquery: any, tavernHelper: any): void {
    $ = jquery;
    TavernHelper = tavernHelper;
    const parentDoc = parentWin.document;

    console.log('[WorldInfoOptimizer] Initializing main application...');

    // 创建主面板
    createMainPanel();

    // 创建扩展菜单按钮
    createExtensionButton();

    // 绑定事件处理器
    bindEventHandlers();

    // 加载初始数据
    loadAllData();

    console.log('[WorldInfoOptimizer] Main application initialized successfully.');
  }

  // --- UI 创建函数 ---
  const createMainPanel = (): void => {
    const parentDoc = parentWin.document;

    // 检查面板是否已存在
    if ($(`#${PANEL_ID}`, parentDoc).length > 0) {
      console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
      return;
    }

    const panelHtml = `
            <div id="${PANEL_ID}" class="wio-panel" style="display: none;">
                <div class="wio-panel-header">
                    <h3 class="wio-panel-title">
                        <i class="fa-solid fa-book"></i> 世界书优化器
                    </h3>
                    <div class="wio-panel-controls">
                        <button id="${REFRESH_BTN_ID}" class="wio-btn wio-btn-icon" title="刷新数据">
                            <i class="fa-solid fa-sync-alt"></i>
                        </button>
                        <button class="wio-btn wio-btn-icon wio-panel-close" title="关闭">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="wio-panel-body">
                    <div class="wio-tabs">
                        <button class="wio-tab-btn active" data-tab="global-lore">全局世界书</button>
                        <button class="wio-tab-btn" data-tab="char-lore">角色世界书</button>
                        <button class="wio-tab-btn" data-tab="chat-lore">聊天世界书</button>
                        <button class="wio-tab-btn" data-tab="global-regex">全局正则</button>
                        <button class="wio-tab-btn" data-tab="char-regex">角色正则</button>
                    </div>
                    <div class="wio-search-section">
                        <div class="wio-search-bar">
                            <input type="text" id="${SEARCH_INPUT_ID}" placeholder="搜索世界书、条目、关键词..." class="wio-search-input">
                            <button class="wio-btn wio-search-btn">
                                <i class="fa-solid fa-search"></i>
                            </button>
                        </div>
                        <div id="wio-search-filters-container" class="wio-search-filters">
                            <label><input type="checkbox" id="wio-filter-book-name" checked> 书名</label>
                            <label><input type="checkbox" id="wio-filter-entry-name" checked> 条目名</label>
                            <label><input type="checkbox" id="wio-filter-keywords" checked> 关键词</label>
                            <label><input type="checkbox" id="wio-filter-content" checked> 内容</label>
                        </div>
                    </div>
                    <div class="wio-toolbar">
                        <button id="${CREATE_LOREBOOK_BTN_ID}" class="wio-btn wio-btn-primary">
                            <i class="fa-solid fa-plus"></i> 新建世界书
                        </button>
                        <button id="${COLLAPSE_ALL_BTN_ID}" class="wio-btn">
                            <i class="fa-solid fa-compress-alt"></i> 全部折叠
                        </button>
                        <button class="wio-btn wio-multi-select-toggle">
                            <i class="fa-solid fa-check-square"></i> 多选模式
                        </button>
                    </div>
                    <div id="${PANEL_ID}-content" class="wio-content">
                        <p class="wio-info-text">正在初始化...</p>
                    </div>
                </div>
            </div>
        `;

    $('body', parentDoc).append(panelHtml);

    // 添加基础样式
    addBasicStyles();
  };

  const createExtensionButton = (): void => {
    const parentDoc = parentWin.document;

    // 检查按钮是否已存在
    if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
      console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
      return;
    }

    const buttonHtml = `
            <div id="${BUTTON_ID}" class="list-group-item flex-container flexGap5" data-i18n="[title]${BUTTON_TOOLTIP}">
                <div class="fa-solid fa-book extensionsMenuExtensionButton" title="${BUTTON_TOOLTIP}"></div>
                <span>${BUTTON_TEXT_IN_MENU}</span>
            </div>
        `;

    const $extensionsMenu = $('#extensionsMenu', parentDoc);
    if ($extensionsMenu.length > 0) {
      $extensionsMenu.append(buttonHtml);
    } else {
      console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');
    }
  };

  const addBasicStyles = (): void => {
    const parentDoc = parentWin.document;

    // 检查样式是否已添加
    if ($('#wio-basic-styles', parentDoc).length > 0) {
      return;
    }

    const basicStyles = `
            <style id="wio-basic-styles">
                .wio-panel {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 90%;
                    max-width: 1200px;
                    height: 80%;
                    background: #2a2a2a;
                    border: 1px solid #444;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                    z-index: 10000;
                    display: flex;
                    flex-direction: column;
                    color: #fff;
                }
                .wio-panel-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #333;
                    border-radius: 8px 8px 0 0;
                }
                .wio-panel-title {
                    margin: 0;
                    font-size: 18px;
                    color: #fff;
                }
                .wio-panel-controls {
                    display: flex;
                    gap: 10px;
                }
                .wio-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #555;
                    color: #fff;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-btn:hover {
                    background: #666;
                }
                .wio-btn-primary {
                    background: #007bff;
                }
                .wio-btn-primary:hover {
                    background: #0056b3;
                }
                .wio-btn-icon {
                    padding: 8px;
                    width: 36px;
                    height: 36px;
                }
                .wio-panel-body {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }
                .wio-tabs {
                    display: flex;
                    border-bottom: 1px solid #444;
                    background: #333;
                }
                .wio-tab-btn {
                    padding: 12px 20px;
                    border: none;
                    background: transparent;
                    color: #ccc;
                    cursor: pointer;
                    border-bottom: 2px solid transparent;
                    transition: all 0.2s;
                }
                .wio-tab-btn:hover {
                    background: #444;
                    color: #fff;
                }
                .wio-tab-btn.active {
                    color: #fff;
                    border-bottom-color: #007bff;
                    background: #444;
                }
                .wio-search-section {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                }
                .wio-search-bar {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 10px;
                }
                .wio-search-input {
                    flex: 1;
                    padding: 10px;
                    border: 1px solid #555;
                    border-radius: 4px;
                    background: #333;
                    color: #fff;
                }
                .wio-search-filters {
                    display: flex;
                    gap: 15px;
                    flex-wrap: wrap;
                }
                .wio-search-filters label {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    color: #ccc;
                    cursor: pointer;
                }
                .wio-toolbar {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }
                .wio-content {
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                    background: #1a1a1a;
                }
                .wio-info-text {
                    text-align: center;
                    color: #888;
                    font-style: italic;
                    margin: 40px 0;
                }
                .wio-book-item {
                    margin-bottom: 20px;
                    padding: 15px;
                    background: #333;
                    border-radius: 6px;
                    border: 1px solid #444;
                }
                .wio-highlight {
                    background: #ffeb3b;
                    color: #000;
                    padding: 1px 2px;
                    border-radius: 2px;
                }
            </style>
        `;

    $('head', parentDoc).append(basicStyles);
  };

  // --- 事件处理器 ---
  const bindEventHandlers = (): void => {
    const parentDoc = parentWin.document;

    // 扩展菜单按钮点击事件
    $(parentDoc).on('click', `#${BUTTON_ID}`, () => {
      const $panel = $(`#${PANEL_ID}`, parentDoc);
      if ($panel.is(':visible')) {
        $panel.hide();
      } else {
        $panel.show();
        if (!appState.isDataLoaded) {
          loadAllData();
        }
      }
    });

    // 面板关闭按钮
    $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {
      $(`#${PANEL_ID}`, parentDoc).hide();
    });

    // 刷新按钮
    $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {
      loadAllData();
    });

    // 标签页切换
    $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, function () {
      const $this = $(this);
      const tabId = $this.data('tab');

      $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');
      $this.addClass('active');

      appState.activeTab = tabId;
      renderContent();
    });

    // 搜索输入
    $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {
      renderContent();
    });

    // 搜索过滤器
    $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type="checkbox"]`, () => {
      renderContent();
    });

    // 新建世界书按钮
    $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {
      try {
        const bookName = await showModal({
          type: 'prompt',
          title: '新建世界书',
          text: '请输入世界书名称：',
          placeholder: '世界书名称',
        });

        if (bookName && typeof bookName === 'string') {
          const progressToast = showProgressToast('正在创建世界书...');
          await TavernAPI.createLorebook(bookName.trim());
          progressToast.remove();
          showSuccessTick(`世界书 "${bookName}" 创建成功`);
          loadAllData(); // 重新加载数据
        }
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error creating lorebook:', error);
      }
    });

    // 全部折叠按钮
    $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {
      $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');
    });

    // 多选模式切换
    $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, function () {
      appState.multiSelectMode = !appState.multiSelectMode;
      const $this = $(this);

      if (appState.multiSelectMode) {
        $this.addClass('active').html('<i class="fa-solid fa-times"></i> 退出多选');
      } else {
        $this.removeClass('active').html('<i class="fa-solid fa-check-square"></i> 多选模式');
        appState.selectedItems.clear();
      }

      renderContent();
    });

    // ESC键关闭面板
    $(parentDoc).on('keydown', (e: any) => {
      if (e.key === 'Escape') {
        const $panel = $(`#${PANEL_ID}`, parentDoc);
        if ($panel.is(':visible')) {
          $panel.hide();
        }
      }
    });

    console.log('[WorldInfoOptimizer] Event handlers bound successfully.');
  };

  // --- 初始化脚本 ---
  console.log('[WorldInfoOptimizer] Starting initialization...');
  onReady(main);
})();
