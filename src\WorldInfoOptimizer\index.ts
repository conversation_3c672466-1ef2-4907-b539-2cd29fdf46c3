// ==UserScript==
// @name         世界书优化器 (World Info Optimizer)
// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0
// @match        */*
// @version      1.0.0
// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。
// <AUTHOR> & AI Assistant
// @grant        none
// @inject-into  content
// ==/UserScript==

'use strict';

// 使用IIFE封装，避免全局污染
(() => {
  console.log('[WorldInfoOptimizer] Script execution started.');

  // --- 类型定义 ---
  interface LorebookEntry {
    uid: string;
    comment: string;
    content: string;
    keys: string[];
    enabled: boolean;
    display_index: number;
    [key: string]: any;
  }

  interface AppState {
    regexes: {
      global: any[];
      character: any[];
    };
    lorebooks: {
      character: string[];
    };
    chatLorebook: string | null;
    allLorebooks: Array<{ name: string; enabled: boolean }>;
    lorebookEntries: Map<string, LorebookEntry[]>;
    lorebookUsage: Map<string, string[]>;
    activeTab: string;
    isDataLoaded: boolean;
    searchFilters: {
      bookName: boolean;
      entryName: boolean;
      keywords: boolean;
      content: boolean;
    };
    multiSelectMode: boolean;
    selectedItems: Set<string>;
  }

  // --- 配置常量 ---
  const SCRIPT_VERSION_TAG = 'v1_0_0';
  const PANEL_ID = 'world-info-optimizer-panel';
  const BUTTON_ID = 'world-info-optimizer-button';
  const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';
  const BUTTON_TOOLTIP = '世界书优化器';
  const BUTTON_TEXT_IN_MENU = '世界书优化器';
  const SEARCH_INPUT_ID = 'wio-search-input';
  const REFRESH_BTN_ID = 'wio-refresh-btn';
  const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';
  const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';
  const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';

  const LOREBOOK_OPTIONS = {
    position: {
      before_character_definition: '角色定义前',
      after_character_definition: '角色定义后',
      before_example_messages: '聊天示例前',
      after_example_messages: '聊天示例后',
      before_author_note: '作者笔记前',
      after_author_note: '作者笔记后',
      at_depth_as_system: '@D ⚙ 系统',
      at_depth_as_assistant: '@D 🗨️ 角色',
      at_depth_as_user: '@D 👤 用户',
    },
    logic: {
      and_any: '任一 AND',
      and_all: '所有 AND',
      not_any: '任一 NOT',
      not_all: '所有 NOT',
    },
  };

  // --- 应用程序状态 ---
  const appState: AppState = {
    regexes: { global: [], character: [] },
    lorebooks: { character: [] },
    chatLorebook: null,
    allLorebooks: [],
    lorebookEntries: new Map(),
    lorebookUsage: new Map(),
    activeTab: 'global-lore',
    isDataLoaded: false,
    searchFilters: { bookName: true, entryName: true, keywords: true, content: true },
    multiSelectMode: false,
    selectedItems: new Set(),
  };

  // --- 全局变量 ---
  let parentWin: any;
  let $: any;
  let TavernHelper: any;

  /**
   * 等待DOM和API就绪
   */
  function onReady(callback: (jquery: any, tavernHelper: any) => void): void {
    const domSelector = '#extensionsMenu';
    const maxRetries = 100;
    let retries = 0;

    console.log(
      `[WorldInfoOptimizer] Starting readiness check. Polling for DOM element "${domSelector}" AND core APIs.`,
    );

    const interval = setInterval(() => {
      const parentDoc = window.parent.document;
      parentWin = window.parent;

      const domReady = parentDoc.querySelector(domSelector) !== null;
      const apiReady =
        parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;

      if (domReady && apiReady) {
        clearInterval(interval);
        console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);
        try {
          callback(parentWin.jQuery, parentWin.TavernHelper);
        } catch (e) {
          console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);
        }
      } else {
        retries++;
        if (retries > maxRetries) {
          clearInterval(interval);
          console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);
          if (!domReady) console.error(`[WorldInfoOptimizer] -> Failure: DOM element "${domSelector}" not found.`);
          if (!apiReady) console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);
        }
      }
    }, 150);
  }

  /**
   * 错误处理包装器
   */
  const errorCatched =
    (fn: Function, context = 'WorldInfoOptimizer') =>
    async (...args: any[]) => {
      try {
        return await fn(...args);
      } catch (error) {
        if (error) {
          console.error(`[${context}] Error:`, error);
          await showModal({
            type: 'alert',
            title: '脚本异常',
            text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,
          });
        }
      }
    };

  // --- 安全访问 lorebookEntries 的函数 ---
  const safeGetLorebookEntries = (bookName: string): LorebookEntry[] => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      if (typeof appState.lorebookEntries.get !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      const entries = appState.lorebookEntries.get(bookName);
      return Array.isArray(entries) ? entries : [];
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      return [];
    }
  };

  const safeSetLorebookEntries = (bookName: string, entries: LorebookEntry[]): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      if (typeof appState.lorebookEntries.set !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
      }

      appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);
    }
  };

  const safeDeleteLorebookEntries = (bookName: string): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      if (typeof appState.lorebookEntries.delete !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      appState.lorebookEntries.delete(bookName);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);
      appState.lorebookEntries = new Map();
    }
  };

  const safeClearLorebookEntries = (): void => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      if (typeof appState.lorebookEntries.clear !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return;
      }

      appState.lorebookEntries.clear();
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);
      appState.lorebookEntries = new Map();
    }
  };

  const safeHasLorebookEntries = (bookName: string): boolean => {
    try {
      if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');
        appState.lorebookEntries = new Map();
        return false;
      }

      if (typeof appState.lorebookEntries.has !== 'function') {
        console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');
        appState.lorebookEntries = new Map();
        return false;
      }

      return appState.lorebookEntries.has(bookName);
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);
      appState.lorebookEntries = new Map();
      return false;
    }
  };

  // --- 工具函数 ---
  const escapeHtml = (text: any): string => {
    if (typeof text !== 'string') return String(text);
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  };

  const highlightText = (text: string, searchTerm: string): string => {
    if (!searchTerm || !text) return escapeHtml(text);

    const escapedText = escapeHtml(text);
    const htmlSafeSearchTerm = escapeHtml(searchTerm);
    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');

    return escapedText.replace(regex, '<mark class="wio-highlight">$1</mark>');
  };

  // --- 通知和模态框函数 ---
  const showSuccessTick = (message = '操作成功', duration = 1500): void => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0) return;

    $panel.find('.wio-toast-notification').remove();

    const toastHtml = `<div class="wio-toast-notification"><i class="fa-solid fa-check-circle"></i> ${escapeHtml(message)}</div>`;
    const $toast = $(toastHtml);

    $panel.append($toast);

    setTimeout(() => {
      $toast.addClass('visible');
    }, 10);

    setTimeout(() => {
      $toast.removeClass('visible');
      setTimeout(() => {
        $toast.remove();
      }, 300);
    }, duration);
  };

  const showProgressToast = (initialMessage = '正在处理...') => {
    const $panel = $(`#${PANEL_ID}`, parentWin.document);
    if ($panel.length === 0) return { update: () => {}, remove: () => {} };

    $panel.find('.wio-progress-toast').remove();

    const toastHtml = `<div class="wio-progress-toast"><i class="fa-solid fa-spinner fa-spin"></i> <span class="wio-progress-text">${escapeHtml(initialMessage)}</span></div>`;
    const $toast = $(toastHtml);

    $panel.append($toast);

    setTimeout(() => {
      $toast.addClass('visible');
    }, 10);

    const update = (newMessage: string) => {
      $toast.find('.wio-progress-text').html(escapeHtml(newMessage));
    };

    const remove = () => {
      $toast.removeClass('visible');
      setTimeout(() => {
        $toast.remove();
      }, 300);
    };

    return { update, remove };
  };

  interface ModalOptions {
    type?: 'alert' | 'confirm' | 'prompt';
    title?: string;
    text?: string;
    placeholder?: string;
    value?: string;
  }

  const showModal = (options: ModalOptions): Promise<any> => {
    return new Promise((resolve, reject) => {
      const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;
      let buttonsHtml = '';
      if (type === 'alert') buttonsHtml = '<button class="wio-modal-btn wio-modal-ok">确定</button>';
      else if (type === 'confirm')
        buttonsHtml =
          '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确认</button>';
      else if (type === 'prompt')
        buttonsHtml =
          '<button class="wio-modal-btn wio-modal-cancel">取消</button><button class="wio-modal-btn wio-modal-ok">确定</button>';

      const inputHtml =
        type === 'prompt'
          ? `<input type="text" class="wio-modal-input" placeholder="${escapeHtml(placeholder)}" value="${escapeHtml(value)}">`
          : '';

      const modalHtml = `<div class="wio-modal-overlay"><div class="wio-modal-content"><div class="wio-modal-header">${escapeHtml(title)}</div><div class="wio-modal-body"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class="wio-modal-footer">${buttonsHtml}</div></div></div>`;

      const $modal = $(modalHtml).hide();
      const $panel = $(`#${PANEL_ID}`, parentWin.document);
      if ($panel.length > 0) {
        $panel.append($modal);
      } else {
        $('body', parentWin.document).append($modal);
      }

      $modal.fadeIn(200);
      const $input = $modal.find('.wio-modal-input');
      if (type === 'prompt') $input.focus().select();

      const closeModal = (isSuccess: boolean, val?: any) => {
        $modal.fadeOut(200, () => {
          $modal.remove();
          if (isSuccess) resolve(val);
          else reject();
        });
      };

      $modal.on('click', '.wio-modal-ok', () => {
        const val = type === 'prompt' ? $input.val() : true;
        if (type === 'prompt' && !String(val).trim()) {
          $input.addClass('wio-input-error');
          setTimeout(() => $input.removeClass('wio-input-error'), 500);
          return;
        }
        closeModal(true, val);
      });
      $modal.on('click', '.wio-modal-cancel', () => closeModal(false));
      if (type === 'prompt') {
        $input.on('keydown', (e: any) => {
          if (e.key === 'Enter') $modal.find('.wio-modal-ok').click();
          else if (e.key === 'Escape') closeModal(false);
        });
      }
    });
  };

  // --- API 包装器 ---
  const TavernAPI = {
    createLorebook: errorCatched(async (name: string) => await TavernHelper.createLorebook(name)),
    deleteLorebook: errorCatched(async (name: string) => await TavernHelper.deleteLorebook(name)),
    getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),
    setLorebookSettings: errorCatched(async (settings: any) => await TavernHelper.setLorebookSettings(settings)),
    getCharData: errorCatched(async () => await TavernHelper.getCharData()),
    Character: TavernHelper.Character,
    getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),
    replaceRegexes: errorCatched(
      async (regexes: any) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' }),
    ),
    getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),
    getCharLorebooks: errorCatched(async (charData?: any) => await TavernHelper.getCharLorebooks(charData)),
    getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),
    getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),
    getOrCreateChatLorebook: errorCatched(async (name: string) => await TavernHelper.getOrCreateChatLorebook(name)),
    setChatLorebook: errorCatched(async (name: string) => await TavernHelper.setChatLorebook(name)),
    getLorebookEntries: errorCatched(async (name: string) => await TavernHelper.getLorebookEntries(name)),
    setLorebookEntries: errorCatched(
      async (name: string, entries: LorebookEntry[]) => await TavernHelper.setLorebookEntries(name, entries),
    ),
    createLorebookEntries: errorCatched(
      async (name: string, entries: LorebookEntry[]) => await TavernHelper.createLorebookEntries(name, entries),
    ),
    deleteLorebookEntries: errorCatched(
      async (name: string, uids: string[]) => await TavernHelper.deleteLorebookEntries(name, uids),
    ),
    saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),
    setCurrentCharLorebooks: errorCatched(
      async (lorebooks: any) => await TavernHelper.setCurrentCharLorebooks(lorebooks),
    ),
  };

  // --- 数据加载函数 ---
  const loadAllData = errorCatched(async () => {
    const $content = $(`#${PANEL_ID}-content`, parentWin.document);
    $content.html('<p class="wio-info-text">正在加载所有数据，请稍候...</p>');

    try {
      // 防御性检查：确保SillyTavern API可用
      if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {
        console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');
        appState.regexes.global = [];
        appState.regexes.character = [];
        appState.allLorebooks = [];
        appState.lorebooks.character = [];
        appState.chatLorebook = null;
        safeClearLorebookEntries();
        appState.isDataLoaded = true;
        renderContent();
        return;
      }

      const context = parentWin.SillyTavern.getContext() || {};
      const allCharacters = Array.isArray(context.characters) ? context.characters : [];
      const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;
      const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

      let charData = null,
        charLinkedBooks = null,
        chatLorebook = null;

      // 使用Promise.allSettled来避免单个失败影响整体
      const promises = [
        TavernAPI.getRegexes().catch(() => []),
        TavernAPI.getLorebookSettings().catch(() => ({})),
        TavernAPI.getLorebooks().catch(() => []),
      ];

      if (hasActiveCharacter) {
        promises.push(TavernAPI.getCharData().catch(() => null));
        promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));
      } else {
        promises.push(Promise.resolve(null), Promise.resolve(null));
      }

      if (hasActiveChat) {
        promises.push(
          TavernAPI.getChatLorebook().catch(error => {
            console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);
            return null;
          }),
        );
      } else {
        promises.push(Promise.resolve(null));
      }

      const results = await Promise.allSettled(promises);

      // 安全提取结果
      const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];
      const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};
      const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];
      charData = results[3]?.status === 'fulfilled' ? results[3].value : null;
      charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;
      chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;

      appState.regexes.global = Array.isArray(allUIRegexes)
        ? allUIRegexes.filter((r: any) => r.scope === 'global')
        : [];
      updateCharacterRegexes(allUIRegexes, charData);

      safeClearLorebookEntries();
      appState.lorebookUsage.clear();
      const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);

      // 安全处理角色世界书
      if (Array.isArray(allCharacters) && allCharacters.length > 0) {
        try {
          await Promise.all(
            allCharacters.map(async (char: any) => {
              if (!char || !char.name) return;
              try {
                let books = null;
                try {
                  const result = TavernHelper.getCharLorebooks({ name: char.name });
                  if (result && typeof result.then === 'function') {
                    books = await result;
                  } else {
                    books = result;
                  }
                } catch (error) {
                  console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character "${char.name}":`, error);
                  books = null;
                }
                if (books && typeof books === 'object') {
                  const bookSet = new Set();
                  if (books.primary && typeof books.primary === 'string') bookSet.add(books.primary);
                  if (Array.isArray(books.additional)) {
                    books.additional.forEach((b: string) => typeof b === 'string' && bookSet.add(b));
                  }

                  bookSet.forEach(bookName => {
                    if (typeof bookName === 'string') {
                      if (!appState.lorebookUsage.has(bookName)) {
                        appState.lorebookUsage.set(bookName, []);
                      }
                      appState.lorebookUsage.get(bookName)!.push(char.name);
                      knownBookNames.add(bookName);
                      console.log(`[WorldInfoOptimizer] Character "${char.name}" uses lorebook "${bookName}"`);
                    }
                  });
                }
              } catch (charError) {
                console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);
              }
            }),
          );
        } catch (charProcessingError) {
          console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);
        }
      }

      const enabledGlobalBooks = new Set(
        Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : [],
      );
      appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name: string) => ({
        name: name,
        enabled: enabledGlobalBooks.has(name),
      }));

      const charBookSet = new Set();
      if (charLinkedBooks && typeof charLinkedBooks === 'object') {
        if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {
          charBookSet.add(charLinkedBooks.primary);
        }
        if (Array.isArray(charLinkedBooks.additional)) {
          charLinkedBooks.additional.forEach((name: string) => typeof name === 'string' && charBookSet.add(name));
        }
      }
      appState.lorebooks.character = Array.from(charBookSet) as string[];
      appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;
      if (typeof chatLorebook === 'string') {
        knownBookNames.add(chatLorebook);
      }

      const allBooksToLoad = Array.from(knownBookNames);
      const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);

      // 分批加载世界书条目，避免同时加载过多
      const batchSize = 5;
      for (let i = 0; i < allBooksToLoad.length; i += batchSize) {
        const batch = allBooksToLoad.slice(i, i + batchSize);
        await Promise.allSettled(
          batch.map(async name => {
            if (existingBookFiles.has(name) && typeof name === 'string') {
              try {
                const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);
                safeSetLorebookEntries(name, entries);
              } catch (entryError) {
                console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);
              }
            }
          }),
        );
      }

      appState.isDataLoaded = true;
      renderContent();
    } catch (error) {
      console.error('[WorldInfoOptimizer] Error in loadAllData:', error);
      $content.html(`
                <div style="padding: 20px; text-align: center;">
                    <p style="color: #ff6b6b; margin-bottom: 10px;">
                        <i class="fa-solid fa-exclamation-triangle"></i> 数据加载失败
                    </p>
                    <p style="color: #666; font-size: 14px;">
                        请检查开发者控制台获取详细信息，或尝试刷新页面。
                    </p>
                    <button class="wio-modal-btn" onclick="$('#${REFRESH_BTN_ID}').click()"
                            style="margin-top: 15px; padding: 8px 16px;">
                        <i class="fa-solid fa-refresh"></i> 重试
                    </button>
                </div>
            `);
      throw error;
    }
  });

  // --- 角色正则和世界书更新函数 ---
  function updateCharacterRegexes(allUIRegexes: any[], charData: any): void {
    const characterUIRegexes = allUIRegexes?.filter((r: any) => r.scope === 'character') || [];
    let cardRegexes: any[] = [];
    if (charData && TavernAPI.Character) {
      try {
        const character = new TavernAPI.Character(charData);
        cardRegexes = (character.getRegexScripts() || []).map((r: any, i: number) => ({
          id: r.id || `card-${Date.now()}-${i}`,
          script_name: r.scriptName || '未命名卡内正则',
          find_regex: r.findRegex,
          replace_string: r.replaceString,
          enabled: !r.disabled,
          scope: 'character',
          source: 'card',
        }));
      } catch (e) {
        console.warn('无法解析角色卡正则脚本:', e);
      }
    }
    const uiRegexIdentifiers = new Set(
      characterUIRegexes.map((r: any) => `${r.script_name}::${r.find_regex}::${r.replace_string}`),
    );
    const uniqueCardRegexes = cardRegexes.filter((r: any) => {
      const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;
      return !uiRegexIdentifiers.has(identifier);
    });
    appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];
  }

  function updateCharacterLorebooks(charBooks: any): void {
    const characterBookNames: string[] = [];
    if (charBooks) {
      if (charBooks.primary) characterBookNames.push(charBooks.primary);
      if (charBooks.additional) characterBookNames.push(...charBooks.additional);
    }
    appState.lorebooks.character = [...new Set(characterBookNames)];
  }

  // --- 渲染函数 ---
  const renderContent = (): void => {
    const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';
    appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');
    appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');
    appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');
    appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');

    const $content = $(`#${PANEL_ID}-content`, parentWin.document);
    $content.empty();

    $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);
    const isLoreTab =
      appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';
    $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);
    $(`#wio-multi-select-controls`, parentWin.document).toggle(appState.multiSelectMode);

    updateSelectionCount();

    switch (appState.activeTab) {
      case 'global-lore':
        renderGlobalLorebookView(searchTerm, $content);
        break;
      case 'char-lore':
        renderCharacterLorebookView(searchTerm, $content);
        break;
      case 'chat-lore':
        renderChatLorebookView(searchTerm, $content);
        break;
      case 'global-regex':
        renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');
        break;
      case 'char-regex':
        renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');
        break;
    }
  };

  // --- 选择和批量操作函数 ---
  const updateSelectionCount = (): void => {
    $(`#wio-selection-count`, parentWin.document).text(`已选择: ${appState.selectedItems.size}`);
  };

  const getAllVisibleItems = () => {
    const visibleItems: any[] = [];
    const activeTab = appState.activeTab;
    if (activeTab === 'global-lore') {
      appState.allLorebooks.forEach(book => {
        visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });
        [...safeGetLorebookEntries(book.name)].forEach(entry => {
          visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });
        });
      });
    } else if (activeTab === 'char-lore') {
      appState.lorebooks.character.forEach(bookName => {
        [...safeGetLorebookEntries(bookName)].forEach(entry => {
          visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });
        });
      });
    } else if (activeTab === 'global-regex') {
      appState.regexes.global.forEach(regex => {
        visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });
      });
    } else if (activeTab === 'char-regex') {
      appState.regexes.character.forEach(regex => {
        visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });
      });
    }
    return visibleItems;
  };

  const renderGlobalLorebookView = (searchTerm: string, $container: any): void => {
    const books = [...appState.allLorebooks].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name),
    );
    let filteredBookData: any[] = [];

    if (!searchTerm) {
      filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));
    } else {
      books.forEach(book => {
        const entries = [...safeGetLorebookEntries(book.name)];
        const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);
        const matchingEntries = entries.filter(
          entry =>
            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||
            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||
            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)),
        );

        if (bookNameMatches || matchingEntries.length > 0) {
          filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });
        }
      });
    }

    if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {
      $container.html(`<p class="wio-info-text">未找到匹配的世界书。</p>`);
    } else if (appState.allLorebooks.length === 0) {
      $container.html(`<p class="wio-info-text">还没有世界书，点击上方"+"创建一个吧。</p>`);
    }

    filteredBookData.forEach(data => {
      if (data && data.book) {
        $container.append(
          createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries),
        );
      }
    });
  };

  const renderCharacterLorebookView = (searchTerm: string, $container: any): void => {
    const linkedBooks = appState.lorebooks.character;
    const context = parentWin.SillyTavern.getContext();
    const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;

    if (!hasActiveCharacter) {
      $container.html(`<p class="wio-info-text">请先加载一个角色以管理角色世界书。</p>`);
      return;
    }
    if (linkedBooks.length === 0) {
      $container.html(`<p class="wio-info-text">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);
      return;
    }

    const renderBook = (bookName: string) => {
      const $bookContainer = $(`
        <div class="wio-book-group" data-book-name="${escapeHtml(bookName)}">
          <div class="wio-book-group-header">
            <span>${escapeHtml(bookName)}</span>
            <div class="wio-item-controls">
              <button class="wio-action-btn-icon wio-rename-book-btn" title="重命名世界书"><i class="fa-solid fa-pencil"></i></button>
              <button class="wio-action-btn-icon wio-delete-book-btn" title="删除世界书"><i class="fa-solid fa-trash-can"></i></button>
            </div>
          </div>
          <div class="wio-entry-list-wrapper"></div>
        </div>
      `);
      const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');
      const $entryActions = $(
        `<div class="wio-entry-actions"><button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-plus"></i> 新建条目</button><button class="wio-action-btn wio-batch-recursion-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-shield-halved"></i> 全开防递</button><button class="wio-action-btn wio-fix-keywords-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-check-double"></i> 修复关键词</button></div>`,
      );
      $listWrapper.append($entryActions);

      const entries = [...safeGetLorebookEntries(bookName)].sort(
        (a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index,
      );
      const bookNameMatches =
        !searchTerm || (appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm));
      const matchingEntries = entries.filter(
        entry =>
          !searchTerm ||
          (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||
          (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||
          (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)),
      );

      if (!bookNameMatches && matchingEntries.length === 0) return null;

      const entriesToShow = bookNameMatches ? entries : matchingEntries;

      if (entriesToShow.length === 0 && searchTerm) {
        $listWrapper.append(`<p class="wio-info-text-small">无匹配条目</p>`);
      } else {
        entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));
      }
      return $bookContainer;
    };

    let renderedCount = 0;
    linkedBooks.forEach(bookName => {
      const $el = renderBook(bookName);
      if ($el) {
        $container.append($el);
        renderedCount++;
      }
    });

    if (renderedCount === 0 && searchTerm) {
      $container.html(`<p class="wio-info-text">未找到匹配的世界书或条目。</p>`);
    }
  };

  const renderChatLorebookView = (searchTerm: string, $container: any): void => {
    const bookName = appState.chatLorebook;
    const context = parentWin.SillyTavern.getContext();
    const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

    if (!hasActiveChat) {
      $container.html(`<p class="wio-info-text">请先开始一个聊天以管理聊天世界书。</p>`);
      return;
    }

    if (!bookName) {
      $container.html(`
        <div class="wio-info-section">
          <p class="wio-info-text">当前聊天没有绑定世界书。</p>
          <button id="wio-create-chat-lore-btn" class="wio-btn wio-btn-primary">
            <i class="fa-solid fa-plus"></i> 创建聊天世界书
          </button>
        </div>
      `);
      return;
    }

    const $bookContainer = $(`
      <div class="wio-book-group" data-book-name="${escapeHtml(bookName)}">
        <div class="wio-book-group-header">
          <span>${escapeHtml(bookName)} (聊天世界书)</span>
          <div class="wio-item-controls">
            <button class="wio-action-btn-icon wio-unlink-chat-lore-btn" title="解除绑定"><i class="fa-solid fa-unlink"></i></button>
          </div>
        </div>
        <div class="wio-entry-list-wrapper"></div>
      </div>
    `);

    const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');
    const $entryActions = $(
      `<div class="wio-entry-actions"><button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(bookName)}"><i class="fa-solid fa-plus"></i> 新建条目</button></div>`,
    );
    $listWrapper.append($entryActions);

    const entries = [...safeGetLorebookEntries(bookName)].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index,
    );
    const matchingEntries = entries.filter(
      entry =>
        !searchTerm ||
        (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||
        (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||
        (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)),
    );

    if (matchingEntries.length === 0 && searchTerm) {
      $listWrapper.append(`<p class="wio-info-text-small">无匹配条目</p>`);
    } else {
      matchingEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));
    }

    $container.empty().append($bookContainer);
  };

  const renderRegexView = (regexes: any[], searchTerm: string, $container: any, title: string): void => {
    if (regexes.length === 0) {
      $container.html(`<p class="wio-info-text">没有找到${title}。</p>`);
      return;
    }

    // 按启用状态和名称排序
    const sortedRegexes = [...regexes].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''),
    );

    // 过滤匹配项
    let filteredRegexes = sortedRegexes;
    if (searchTerm) {
      filteredRegexes = sortedRegexes.filter(regex => {
        const name = regex.script_name || '';
        const findRegex = regex.find_regex || '';
        const replaceString = regex.replace_string || '';

        return (
          name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||
          replaceString.toLowerCase().includes(searchTerm.toLowerCase())
        );
      });
    }

    if (filteredRegexes.length === 0 && searchTerm) {
      $container.html(`<p class="wio-info-text">未找到匹配的${title}。</p>`);
      return;
    }

    // 添加操作按钮区域
    const $actions = $(`
      <div class="wio-regex-actions">
        <button class="wio-action-btn wio-create-regex-btn" data-scope="${title === '全局正则' ? 'global' : 'character'}">
          <i class="fa-solid fa-plus"></i> 新建正则
        </button>
        <button class="wio-action-btn wio-import-regex-btn">
          <i class="fa-solid fa-upload"></i> 导入正则
        </button>
        <button class="wio-action-btn wio-export-regex-btn">
          <i class="fa-solid fa-download"></i> 导出正则
        </button>
      </div>
    `);
    $container.append($actions);

    // 渲染正则列表
    const $regexList = $('<div class="wio-regex-list"></div>');
    filteredRegexes.forEach(regex => {
      $regexList.append(createItemElement(regex, 'regex', '', searchTerm));
    });
    $container.append($regexList);
  };

  // --- 核心UI元素创建函数 ---
  const createItemElement = (item: LorebookEntry | any, type: string, bookName = '', searchTerm = ''): any => {
    const isLore = type === 'lore';
    const id = isLore ? item.uid : item.id;
    const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';
    const fromCard = item.source === 'card';

    let controlsHtml = '';

    if (isLore) {
      // 所有世界书条目都有完整的操作按钮
      controlsHtml = `
        <button class="wio-action-btn-icon wio-rename-btn" title="重命名"><i class="fa-solid fa-pencil"></i></button>
        <button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>
        <button class="wio-action-btn-icon wio-delete-entry-btn" title="删除条目"><i class="fa-solid fa-trash-can"></i></button>
      `;
    } else if (fromCard) {
      // 来自卡片的正则只有开关
      controlsHtml =
        '<button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>';
    } else {
      // UI中的正则有重命名和开关
      controlsHtml = `
        <button class="wio-action-btn-icon wio-rename-btn" title="重命名"><i class="fa-solid fa-pencil"></i></button>
        <button class="wio-toggle-btn wio-item-toggle" title="启用/禁用此条目"><i class="fa-solid fa-power-off"></i></button>
      `;
    }

    const dragHandleHtml =
      !fromCard && !isLore
        ? '<span class="wio-drag-handle" title="拖拽排序"><i class="fa-solid fa-grip-vertical"></i></span>'
        : '';

    // 应用高亮到条目名称
    const highlightedName = highlightText(name, searchTerm);

    const $element = $(
      `<div class="wio-item-container ${fromCard ? 'from-card' : ''}" data-type="${type}" data-id="${id}" ${isLore ? `data-book-name="${escapeHtml(bookName)}"` : ''}><div class="wio-item-header" title="${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}">${dragHandleHtml}<span class="wio-item-name">${highlightedName}</span><div class="wio-item-controls">${controlsHtml}</div></div><div class="wio-collapsible-content"></div></div>`,
    );

    // 保存搜索词以便在内容展开时使用
    $element.data('searchTerm', searchTerm);

    $element.toggleClass('enabled', item.enabled);

    if (appState.multiSelectMode) {
      const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;
      $element.toggleClass('selected', appState.selectedItems.has(itemKey));
    }

    return $element;
  };

  const createGlobalLorebookElement = (
    book: any,
    searchTerm: string,
    forceShowAllEntries: boolean,
    filteredEntries: any[],
  ): any => {
    const usedByChars = appState.lorebookUsage.get(book.name) || [];
    const usedByHtml =
      usedByChars.length > 0
        ? `<div class="wio-used-by-chars">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`
        : '';

    const $element = $(`
      <div class="wio-book-group" data-book-name="${escapeHtml(book.name)}">
        <div class="wio-global-book-header" title="${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}">
          <div class="wio-book-info">
            <span class="wio-book-name">${highlightText(book.name, searchTerm)}</span>
            <span class="wio-book-status ${book.enabled ? 'enabled' : 'disabled'}">${book.enabled ? '已启用' : '已禁用'}</span>
            ${usedByHtml}
          </div>
          <div class="wio-item-controls">
            <button class="wio-action-btn-icon wio-rename-book-btn" title="重命名世界书"><i class="fa-solid fa-pencil"></i></button>
            <button class="wio-action-btn-icon wio-edit-entries-btn" title="编辑条目"><i class="fa-solid fa-edit"></i></button>
            <button class="wio-action-btn-icon wio-delete-book-btn" title="删除世界书"><i class="fa-solid fa-trash-can"></i></button>
          </div>
        </div>
        <div class="wio-collapsible-content"></div>
      </div>
    `);

    const $content = $element.find('.wio-collapsible-content');

    // 添加条目操作按钮
    const $entryActions = $(`
      <div class="wio-entry-actions">
        <button class="wio-action-btn wio-create-entry-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-plus"></i> 新建条目
        </button>
        <button class="wio-action-btn wio-batch-recursion-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-shield-halved"></i> 全开防递
        </button>
        <button class="wio-action-btn wio-fix-keywords-btn" data-book-name="${escapeHtml(book.name)}">
          <i class="fa-solid fa-check-double"></i> 修复关键词
        </button>
      </div>
    `);
    $content.append($entryActions);

    const allEntries = [...safeGetLorebookEntries(book.name)].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index,
    );
    const entriesToShow = forceShowAllEntries ? allEntries : filteredEntries || [];

    if (entriesToShow && entriesToShow.length > 0) {
      const $listWrapper = $('<div class="wio-entry-list-wrapper"></div>');
      entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));
      $content.append($listWrapper);
    } else if (searchTerm) {
      $content.append(`<div class="wio-info-text-small">无匹配项</div>`);
    }

    return $element;
  };

  // --- 替换功能实现 ---
  const handleReplace = errorCatched(async () => {
    const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val() as string;
    const replaceTerm = $('#wio-replace-input', parentWin.document).val() as string;

    // 检查搜索词是否为空
    if (!searchTerm) {
      await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });
      return;
    }

    // 检查替换词是否为空
    if (!replaceTerm) {
      await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });
      return;
    }

    // 获取当前视图的匹配项
    let matches: any[] = [];

    switch (appState.activeTab) {
      case 'global-lore':
        matches = getGlobalLorebookMatches(searchTerm);
        break;
      case 'char-lore':
        matches = getCharacterLorebookMatches(searchTerm);
        break;
      case 'chat-lore':
        matches = getChatLorebookMatches(searchTerm);
        break;
      default:
        await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });
        return;
    }

    // 如果没有匹配项，提示用户
    if (matches.length === 0) {
      await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });
      return;
    }

    // 显示确认对话框
    const confirmResult = await showModal({
      type: 'confirm',
      title: '确认替换',
      text: `找到 ${matches.length} 个匹配项。\n\n确定要将 "${searchTerm}" 替换为 "${replaceTerm}" 吗？\n\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\n此操作不可撤销，请谨慎操作。`,
    });

    // 如果用户确认替换，则执行替换
    if (confirmResult) {
      const progressToast = showProgressToast('正在执行替换...');
      try {
        await performReplace(matches, searchTerm, replaceTerm);
        progressToast.remove();
        showSuccessTick('替换完成');
        // 刷新视图
        renderContent();
      } catch (error) {
        progressToast.remove();
        console.error('[WorldInfoOptimizer] Replace error:', error);
        await showModal({
          type: 'alert',
          title: '替换失败',
          text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',
        });
      }
    }
  });

  // 执行替换操作的函数
  const performReplace = async (matches: any[], searchTerm: string, replaceTerm: string) => {
    // 创建一个映射来跟踪每个世界书的更改
    const bookUpdates = new Map();

    // 遍历所有匹配项
    for (const match of matches) {
      const { bookName, entry } = match;
      let updated = false;

      // 如果还没有为这个世界书创建更新数组，则创建一个
      if (!bookUpdates.has(bookName)) {
        bookUpdates.set(bookName, []);
      }

      // 创建条目的深拷贝以进行修改
      const updatedEntry = JSON.parse(JSON.stringify(entry));

      // 替换关键词
      if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {
        const newKeys = updatedEntry.keys.map((key: string) =>
          key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replaceTerm),
        );
        // 检查是否有实际更改
        if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {
          updatedEntry.keys = newKeys;
          updated = true;
        }
      }

      // 替换条目内容
      if (updatedEntry.content) {
        const newContent = updatedEntry.content.replace(
          new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
          replaceTerm,
        );
        if (updatedEntry.content !== newContent) {
          updatedEntry.content = newContent;
          updated = true;
        }
      }

      // 替换条目名称（comment）
      if (updatedEntry.comment) {
        const newComment = updatedEntry.comment.replace(
          new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
          replaceTerm,
        );
        if (updatedEntry.comment !== newComment) {
          updatedEntry.comment = newComment;
          updated = true;
        }
      }

      // 如果有更改，则将更新后的条目添加到更新数组中
      if (updated) {
        bookUpdates.get(bookName).push(updatedEntry);
      }
    }

    // 应用所有更改
    for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {
      if (entriesToUpdate.length > 0) {
        // 调用TavernAPI来更新条目
        const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);
        if (result && result.entries) {
          // 更新本地状态
          safeSetLorebookEntries(bookName, result.entries);
        }
      }
    }

    // 等待一段时间以确保所有操作完成
    await new Promise(resolve => setTimeout(resolve, 100));
  };

  // 获取匹配项的函数
  const getGlobalLorebookMatches = (searchTerm: string) => {
    const matches: any[] = [];
    const books = [...appState.allLorebooks].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name),
    );

    if (!searchTerm) {
      // 如果没有搜索词，返回所有条目
      books.forEach(book => {
        const entries = [...safeGetLorebookEntries(book.name)];
        entries.forEach(entry => {
          matches.push({ bookName: book.name, entry });
        });
      });
    } else {
      // 根据搜索词和过滤器获取匹配项
      books.forEach(book => {
        const entries = [...safeGetLorebookEntries(book.name)];
        const bookNameMatches =
          appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm.toLowerCase());

        entries.forEach(entry => {
          const entryNameMatches =
            appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());
          const keywordsMatch =
            appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());
          const contentMatch =
            appState.searchFilters.content &&
            entry.content &&
            entry.content.toLowerCase().includes(searchTerm.toLowerCase());

          // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
          if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {
            matches.push({ bookName: book.name, entry });
          }
        });
      });
    }

    return matches;
  };

  const getCharacterLorebookMatches = (searchTerm: string) => {
    const matches: any[] = [];
    const linkedBooks = appState.lorebooks.character;
    const context = parentWin.SillyTavern.getContext();
    const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;

    if (!hasActiveCharacter || linkedBooks.length === 0) {
      return matches;
    }

    linkedBooks.forEach(bookName => {
      const entries = [...safeGetLorebookEntries(bookName)].sort(
        (a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index,
      );

      if (!searchTerm) {
        // 如果没有搜索词，返回所有条目
        entries.forEach(entry => {
          matches.push({ bookName, entry });
        });
      } else {
        // 根据搜索词和过滤器获取匹配项
        entries.forEach(entry => {
          const bookNameMatches =
            appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm.toLowerCase());
          const entryNameMatches =
            appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());
          const keywordsMatch =
            appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());
          const contentMatch =
            appState.searchFilters.content &&
            entry.content &&
            entry.content.toLowerCase().includes(searchTerm.toLowerCase());

          // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
          if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {
            matches.push({ bookName, entry });
          }
        });
      }
    });

    return matches;
  };

  const getChatLorebookMatches = (searchTerm: string) => {
    const matches: any[] = [];
    const bookName = appState.chatLorebook;
    const context = parentWin.SillyTavern.getContext();
    const hasActiveChat = context.chatId !== undefined && context.chatId !== null;

    if (!hasActiveChat || !bookName) {
      return matches;
    }

    const entries = [...safeGetLorebookEntries(bookName)].sort(
      (a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index,
    );

    if (!searchTerm) {
      // 如果没有搜索词，返回所有条目
      entries.forEach(entry => {
        matches.push({ bookName, entry });
      });
    } else {
      // 根据搜索词和过滤器获取匹配项
      entries.forEach(entry => {
        const entryNameMatches =
          appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());
        const keywordsMatch =
          appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());
        const contentMatch =
          appState.searchFilters.content &&
          entry.content &&
          entry.content.toLowerCase().includes(searchTerm.toLowerCase());

        // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中
        if (entryNameMatches || keywordsMatch || contentMatch) {
          matches.push({ bookName, entry });
        }
      });
    }

    return matches;
  };

  // --- 主程序逻辑 ---
  function main(jquery: any, tavernHelper: any): void {
    $ = jquery;
    TavernHelper = tavernHelper;

    console.log('[WorldInfoOptimizer] Initializing main application...');

    // 创建主面板
    createMainPanel();

    // 创建扩展菜单按钮
    createExtensionButton();

    // 绑定事件处理器
    bindEventHandlers();

    // 加载初始数据
    loadAllData();

    console.log('[WorldInfoOptimizer] Main application initialized successfully.');
  }

  // --- UI 创建函数 ---
  const createMainPanel = (): void => {
    const parentDoc = parentWin.document;

    // 检查面板是否已存在
    if ($(`#${PANEL_ID}`, parentDoc).length > 0) {
      console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');
      return;
    }

    const panelHtml = `
            <div id="${PANEL_ID}" class="wio-panel" style="display: none;">
                <div class="wio-panel-header">
                    <h3 class="wio-panel-title">
                        <i class="fa-solid fa-book"></i> 世界书优化器
                    </h3>
                    <div class="wio-panel-controls">
                        <button id="${REFRESH_BTN_ID}" class="wio-btn wio-btn-icon" title="刷新数据">
                            <i class="fa-solid fa-sync-alt"></i>
                        </button>
                        <button class="wio-btn wio-btn-icon wio-panel-close" title="关闭">
                            <i class="fa-solid fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="wio-panel-body">
                    <div class="wio-tabs">
                        <button class="wio-tab-btn active" data-tab="global-lore">全局世界书</button>
                        <button class="wio-tab-btn" data-tab="char-lore">角色世界书</button>
                        <button class="wio-tab-btn" data-tab="chat-lore">聊天世界书</button>
                        <button class="wio-tab-btn" data-tab="global-regex">全局正则</button>
                        <button class="wio-tab-btn" data-tab="char-regex">角色正则</button>
                    </div>
                    <div class="wio-search-section">
                        <div class="wio-search-bar">
                            <input type="text" id="${SEARCH_INPUT_ID}" placeholder="搜索世界书、条目、关键词..." class="wio-search-input">
                            <input type="text" id="wio-replace-input" placeholder="替换为..." class="wio-search-input">
                            <button id="wio-replace-btn" class="wio-btn wio-search-btn" title="替换">
                                <i class="fa-solid fa-exchange-alt"></i>
                            </button>
                        </div>
                        <div id="wio-search-filters-container" class="wio-search-filters">
                            <label><input type="checkbox" id="wio-filter-book-name" checked> 书名</label>
                            <label><input type="checkbox" id="wio-filter-entry-name" checked> 条目名</label>
                            <label><input type="checkbox" id="wio-filter-keywords" checked> 关键词</label>
                            <label><input type="checkbox" id="wio-filter-content" checked> 内容</label>
                        </div>
                    </div>
                    <div class="wio-toolbar">
                        <button id="${CREATE_LOREBOOK_BTN_ID}" class="wio-btn wio-btn-primary">
                            <i class="fa-solid fa-plus"></i> 新建世界书
                        </button>
                        <button id="${COLLAPSE_ALL_BTN_ID}" class="wio-btn">
                            <i class="fa-solid fa-compress-alt"></i> 全部折叠
                        </button>
                        <button class="wio-btn wio-multi-select-toggle">
                            <i class="fa-solid fa-check-square"></i> 多选模式
                        </button>
                        <div id="wio-multi-select-controls" class="wio-multi-select-controls" style="display: none;">
                            <div class="wio-multi-select-actions">
                                <button class="wio-multi-select-action-btn" id="wio-select-all-btn">全选</button>
                                <button class="wio-multi-select-action-btn" id="wio-select-none-btn">取消全选</button>
                                <button class="wio-multi-select-action-btn" id="wio-select-invert-btn">反选</button>
                                <button class="wio-multi-select-action-btn enable" id="wio-batch-enable-btn">批量启用</button>
                                <button class="wio-multi-select-action-btn disable" id="wio-batch-disable-btn">批量禁用</button>
                                <button class="wio-multi-select-action-btn disable" id="wio-batch-delete-btn">批量删除</button>
                                <span class="wio-selection-count" id="wio-selection-count">已选择: 0</span>
                            </div>
                        </div>
                    </div>
                    <div id="${PANEL_ID}-content" class="wio-content">
                        <p class="wio-info-text">正在初始化...</p>
                    </div>
                </div>
            </div>
        `;

    $('body', parentDoc).append(panelHtml);

    // 添加基础样式
    addBasicStyles();
  };

  const createExtensionButton = (): void => {
    const parentDoc = parentWin.document;

    // 检查按钮是否已存在
    if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {
      console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');
      return;
    }

    const buttonHtml = `
            <div id="${BUTTON_ID}" class="list-group-item flex-container flexGap5" data-i18n="[title]${BUTTON_TOOLTIP}">
                <div class="fa-solid fa-book extensionsMenuExtensionButton" title="${BUTTON_TOOLTIP}"></div>
                <span>${BUTTON_TEXT_IN_MENU}</span>
            </div>
        `;

    const $extensionsMenu = $('#extensionsMenu', parentDoc);
    if ($extensionsMenu.length > 0) {
      $extensionsMenu.append(buttonHtml);
    } else {
      console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');
    }
  };

  const addBasicStyles = (): void => {
    const parentDoc = parentWin.document;

    // 检查样式是否已添加
    if ($('#wio-basic-styles', parentDoc).length > 0) {
      return;
    }

    const basicStyles = `
            <style id="wio-basic-styles">
                .wio-panel {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 90%;
                    max-width: 1200px;
                    height: 80%;
                    background: #2a2a2a;
                    border: 1px solid #444;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                    z-index: 10000;
                    display: flex;
                    flex-direction: column;
                    color: #fff;
                }
                .wio-panel-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #333;
                    border-radius: 8px 8px 0 0;
                }
                .wio-panel-title {
                    margin: 0;
                    font-size: 18px;
                    color: #fff;
                }
                .wio-panel-controls {
                    display: flex;
                    gap: 10px;
                }
                .wio-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #555;
                    color: #fff;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-btn:hover {
                    background: #666;
                }
                .wio-btn-primary {
                    background: #007bff;
                }
                .wio-btn-primary:hover {
                    background: #0056b3;
                }
                .wio-btn-icon {
                    padding: 8px;
                    width: 36px;
                    height: 36px;
                }
                .wio-panel-body {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                }
                .wio-tabs {
                    display: flex;
                    border-bottom: 1px solid #444;
                    background: #333;
                }
                .wio-tab-btn {
                    padding: 12px 20px;
                    border: none;
                    background: transparent;
                    color: #ccc;
                    cursor: pointer;
                    border-bottom: 2px solid transparent;
                    transition: all 0.2s;
                }
                .wio-tab-btn:hover {
                    background: #444;
                    color: #fff;
                }
                .wio-tab-btn.active {
                    color: #fff;
                    border-bottom-color: #007bff;
                    background: #444;
                }
                .wio-search-section {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                }
                .wio-search-bar {
                    display: flex;
                    gap: 10px;
                    margin-bottom: 10px;
                }
                .wio-search-input {
                    flex: 1;
                    padding: 10px;
                    border: 1px solid #555;
                    border-radius: 4px;
                    background: #333;
                    color: #fff;
                }
                .wio-search-filters {
                    display: flex;
                    gap: 15px;
                    flex-wrap: wrap;
                }
                .wio-search-filters label {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    color: #ccc;
                    cursor: pointer;
                }
                .wio-toolbar {
                    padding: 15px 20px;
                    border-bottom: 1px solid #444;
                    background: #2a2a2a;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }
                .wio-content {
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                    background: #1a1a1a;
                }
                .wio-info-text {
                    text-align: center;
                    color: #888;
                    font-style: italic;
                    margin: 40px 0;
                }
                .wio-book-item {
                    margin-bottom: 20px;
                    padding: 15px;
                    background: #333;
                    border-radius: 6px;
                    border: 1px solid #444;
                }
                .wio-highlight {
                    background: #ffeb3b;
                    color: #000;
                    padding: 1px 2px;
                    border-radius: 2px;
                }
                .wio-item-container {
                    margin-bottom: 10px;
                    border: 1px solid #444;
                    border-radius: 4px;
                    background: #333;
                }
                .wio-item-container.enabled {
                    border-left: 3px solid #28a745;
                }
                .wio-item-container.selected {
                    background: #2a4a6b;
                    border-color: #007bff;
                }
                .wio-item-header {
                    display: flex;
                    align-items: center;
                    padding: 10px 15px;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-item-header:hover {
                    background: #444;
                }
                .wio-item-name {
                    flex: 1;
                    margin-left: 10px;
                    font-weight: 500;
                }
                .wio-item-controls {
                    display: flex;
                    gap: 5px;
                }
                .wio-action-btn-icon {
                    padding: 4px 8px;
                    border: none;
                    border-radius: 3px;
                    background: #555;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-action-btn-icon:hover {
                    background: #666;
                }
                .wio-toggle-btn {
                    padding: 4px 8px;
                    border: none;
                    border-radius: 3px;
                    background: #dc3545;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-toggle-btn:hover {
                    background: #c82333;
                }
                .wio-book-group {
                    margin-bottom: 20px;
                    border: 1px solid #444;
                    border-radius: 6px;
                    background: #2a2a2a;
                }
                .wio-book-group-header, .wio-global-book-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 15px;
                    background: #333;
                    border-radius: 6px 6px 0 0;
                    cursor: pointer;
                    transition: background 0.2s;
                }
                .wio-book-group-header:hover, .wio-global-book-header:hover {
                    background: #444;
                }
                .wio-book-name {
                    font-size: 16px;
                    font-weight: 600;
                    color: #fff;
                }
                .wio-book-status {
                    margin-left: 10px;
                    padding: 2px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 500;
                }
                .wio-book-status.enabled {
                    background: #28a745;
                    color: #fff;
                }
                .wio-book-status.disabled {
                    background: #6c757d;
                    color: #fff;
                }
                .wio-entry-actions, .wio-regex-actions {
                    padding: 15px;
                    border-bottom: 1px solid #444;
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                }
                .wio-action-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    background: #007bff;
                    color: #fff;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.2s;
                }
                .wio-action-btn:hover {
                    background: #0056b3;
                }
                .wio-multi-select-controls {
                    margin-top: 10px;
                    padding: 10px;
                    background: #333;
                    border-radius: 4px;
                }
                .wio-multi-select-actions {
                    display: flex;
                    gap: 10px;
                    flex-wrap: wrap;
                    align-items: center;
                }
                .wio-multi-select-action-btn {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    background: #6c757d;
                    color: #fff;
                    cursor: pointer;
                    font-size: 12px;
                    transition: background 0.2s;
                }
                .wio-multi-select-action-btn:hover {
                    background: #5a6268;
                }
                .wio-multi-select-action-btn.enable {
                    background: #28a745;
                }
                .wio-multi-select-action-btn.enable:hover {
                    background: #218838;
                }
                .wio-multi-select-action-btn.disable {
                    background: #dc3545;
                }
                .wio-multi-select-action-btn.disable:hover {
                    background: #c82333;
                }
                .wio-selection-count {
                    margin-left: auto;
                    color: #ccc;
                    font-size: 12px;
                }
                .wio-info-text-small {
                    text-align: center;
                    color: #888;
                    font-style: italic;
                    margin: 20px 0;
                    font-size: 14px;
                }
                .wio-used-by-chars {
                    margin-top: 5px;
                    font-size: 12px;
                    color: #aaa;
                }
                .wio-used-by-chars span {
                    background: #555;
                    padding: 2px 6px;
                    border-radius: 3px;
                    margin-right: 5px;
                }
                .wio-toast-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: #fff;
                    padding: 12px 20px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10001;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                }
                .wio-toast-notification.visible {
                    opacity: 1;
                    transform: translateX(0);
                }
                .wio-progress-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #007bff;
                    color: #fff;
                    padding: 12px 20px;
                    border-radius: 6px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                    z-index: 10001;
                    opacity: 0;
                    transform: translateX(100%);
                    transition: all 0.3s ease;
                }
                .wio-progress-toast.visible {
                    opacity: 1;
                    transform: translateX(0);
                }
                .wio-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.7);
                    z-index: 10002;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .wio-modal-content {
                    background: #2a2a2a;
                    border-radius: 8px;
                    max-width: 500px;
                    width: 90%;
                    max-height: 80vh;
                    overflow-y: auto;
                }
                .wio-modal-header {
                    padding: 20px;
                    border-bottom: 1px solid #444;
                    font-size: 18px;
                    font-weight: 600;
                    color: #fff;
                }
                .wio-modal-body {
                    padding: 20px;
                    color: #ccc;
                }
                .wio-modal-input {
                    width: 100%;
                    padding: 10px;
                    margin-top: 10px;
                    border: 1px solid #555;
                    border-radius: 4px;
                    background: #333;
                    color: #fff;
                }
                .wio-modal-input.wio-input-error {
                    border-color: #dc3545;
                    animation: shake 0.5s;
                }
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
                .wio-modal-footer {
                    padding: 20px;
                    border-top: 1px solid #444;
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                .wio-modal-btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.2s;
                }
                .wio-modal-ok {
                    background: #007bff;
                    color: #fff;
                }
                .wio-modal-ok:hover {
                    background: #0056b3;
                }
                .wio-modal-cancel {
                    background: #6c757d;
                    color: #fff;
                }
                .wio-modal-cancel:hover {
                    background: #5a6268;
                }
            </style>
        `;

    $('head', parentDoc).append(basicStyles);
  };

  // --- 事件处理器 ---
  const bindEventHandlers = (): void => {
    const parentDoc = parentWin.document;

    // 扩展菜单按钮点击事件
    $(parentDoc).on('click', `#${BUTTON_ID}`, () => {
      const $panel = $(`#${PANEL_ID}`, parentDoc);
      if ($panel.is(':visible')) {
        $panel.hide();
      } else {
        $panel.show();
        if (!appState.isDataLoaded) {
          loadAllData();
        }
      }
    });

    // 面板关闭按钮
    $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {
      $(`#${PANEL_ID}`, parentDoc).hide();
    });

    // 刷新按钮
    $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {
      loadAllData();
    });

    // 标签页切换
    $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event: any) => {
      const $this = $(event.currentTarget);
      const tabId = $this.data('tab');

      $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');
      $this.addClass('active');

      appState.activeTab = tabId;
      renderContent();
    });

    // 搜索输入
    $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {
      renderContent();
    });

    // 搜索过滤器
    $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type="checkbox"]`, () => {
      renderContent();
    });

    // 替换按钮
    $(parentDoc).on('click', '#wio-replace-btn', () => {
      handleReplace();
    });

    // 新建世界书按钮
    $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {
      try {
        const bookName = await showModal({
          type: 'prompt',
          title: '新建世界书',
          text: '请输入世界书名称：',
          placeholder: '世界书名称',
        });

        if (bookName && typeof bookName === 'string') {
          const progressToast = showProgressToast('正在创建世界书...');
          await TavernAPI.createLorebook(bookName.trim());
          progressToast.remove();
          showSuccessTick(`世界书 "${bookName}" 创建成功`);
          loadAllData(); // 重新加载数据
        }
      } catch (error) {
        console.error('[WorldInfoOptimizer] Error creating lorebook:', error);
      }
    });

    // 全部折叠按钮
    $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {
      $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');
    });

    // 多选模式切换
    $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event: any) => {
      appState.multiSelectMode = !appState.multiSelectMode;
      const $this = $(event.currentTarget);

      if (appState.multiSelectMode) {
        $this.addClass('active').html('<i class="fa-solid fa-times"></i> 退出多选');
      } else {
        $this.removeClass('active').html('<i class="fa-solid fa-check-square"></i> 多选模式');
        appState.selectedItems.clear();
      }

      renderContent();
    });

    // ESC键关闭面板
    $(parentDoc).on('keydown', (e: any) => {
      if (e.key === 'Escape') {
        const $panel = $(`#${PANEL_ID}`, parentDoc);
        if ($panel.is(':visible')) {
          $panel.hide();
        }
      }
    });

    console.log('[WorldInfoOptimizer] Event handlers bound successfully.');
  };

  // --- 初始化脚本 ---
  console.log('[WorldInfoOptimizer] Starting initialization...');
  onReady(main);
})();
