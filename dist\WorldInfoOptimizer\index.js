var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!searchTerm || !text)\n            return escapeHtml(text);\n        const escapedText = escapeHtml(text);\n        const htmlSafeSearchTerm = escapeHtml(searchTerm);\n        const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n        return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    const TavernAPI = {\n        createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n        deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n        getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n        setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n        getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n        Character: TavernHelper.Character,\n        getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n        replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n        getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n        getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n        getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n        getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n        getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n        setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n        getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n        setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n        createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n        deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n        saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n        setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html('<p class=\"wio-info-text\">正在加载所有数据，请稍候...</p>');\n        try {\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch(error => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    await Promise.all(allCharacters.map(async (char) => {\n                        if (!char || !char.name)\n                            return;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                if (result && typeof result.then === 'function') {\n                                    books = await result;\n                                }\n                                else {\n                                    books = result;\n                                }\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                                books = null;\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                        console.log(`[WorldInfoOptimizer] Character \"${char.name}\" uses lorebook \"${bookName}\"`);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                    }));\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 分批加载世界书条目，避免同时加载过多\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n            }\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 占位符函数（需要实现） ---\n    const updateSelectionCount = () => {\n        // TODO: 实现选择计数更新\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        let filteredBookData = [];\n        if (!searchTerm) {\n            filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));\n        }\n        else {\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);\n                const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n                if (bookNameMatches || matchingEntries.length > 0) {\n                    filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });\n                }\n            });\n        }\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        // TODO: 实现角色世界书视图渲染\n        $container.html(`<p class=\"wio-info-text\">角色世界书视图开发中...</p>`);\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        // TODO: 实现聊天世界书视图渲染\n        $container.html(`<p class=\"wio-info-text\">聊天世界书视图开发中...</p>`);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        // TODO: 实现正则视图渲染\n        $container.html(`<p class=\"wio-info-text\">${title}视图开发中...</p>`);\n    };\n    const createGlobalLorebookElement = (book, searchTerm, forceShowAllEntries, filteredEntries) => {\n        // TODO: 实现全局世界书元素创建\n        return $(`<div class=\"wio-book-item\"><h3>${escapeHtml(book.name)}</h3><p>开发中...</p></div>`);\n    };\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        const parentDoc = parentWin.document;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 创建主面板\n        createMainPanel();\n        // 创建扩展菜单按钮\n        createExtensionButton();\n        // 绑定事件处理器\n        bindEventHandlers();\n        // 加载初始数据\n        loadAllData();\n        console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <button class=\"wio-btn wio-search-btn\">\r\n                                <i class=\"fa-solid fa-search\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5\" data-i18n=\"[title]${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                .wio-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 90%;\r\n                    max-width: 1200px;\r\n                    height: 80%;\r\n                    background: #2a2a2a;\r\n                    border: 1px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);\r\n                    z-index: 10000;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                $panel.hide();\n            }\n            else {\n                $panel.show();\n                if (!appState.isDataLoaded) {\n                    loadAllData();\n                }\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            $(`#${PANEL_ID}`, parentDoc).hide();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, function () {\n            const $this = $(this);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, function () {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            const $this = $(this);\n            if (appState.multiSelectMode) {\n                $this.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n            }\n            else {\n                $this.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    $panel.hide();\n                }\n            }\n        });\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();