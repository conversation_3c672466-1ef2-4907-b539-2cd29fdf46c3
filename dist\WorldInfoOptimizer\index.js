var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!searchTerm || !text)\n            return escapeHtml(text);\n        const escapedText = escapeHtml(text);\n        const htmlSafeSearchTerm = escapeHtml(searchTerm);\n        const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n        return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    const TavernAPI = {\n        createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n        deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n        getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n        setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n        getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n        Character: TavernHelper.Character,\n        getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n        replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n        getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n        getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n        getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n        getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n        getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n        setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n        getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n        setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n        createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n        deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n        saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n        setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html('<p class=\"wio-info-text\">正在加载所有数据，请稍候...</p>');\n        try {\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch(error => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    await Promise.all(allCharacters.map(async (char) => {\n                        if (!char || !char.name)\n                            return;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                if (result && typeof result.then === 'function') {\n                                    books = await result;\n                                }\n                                else {\n                                    books = result;\n                                }\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                                books = null;\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                        console.log(`[WorldInfoOptimizer] Character \"${char.name}\" uses lorebook \"${bookName}\"`);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                    }));\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 分批加载世界书条目，避免同时加载过多\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n            }\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(appState.multiSelectMode);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 选择和批量操作函数 ---\n    const updateSelectionCount = () => {\n        $(`#wio-selection-count`, parentWin.document).text(`已选择: ${appState.selectedItems.size}`);\n    };\n    const getAllVisibleItems = () => {\n        const visibleItems = [];\n        const activeTab = appState.activeTab;\n        if (activeTab === 'global-lore') {\n            appState.allLorebooks.forEach(book => {\n                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });\n                [...safeGetLorebookEntries(book.name)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'char-lore') {\n            appState.lorebooks.character.forEach(bookName => {\n                [...safeGetLorebookEntries(bookName)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'global-regex') {\n            appState.regexes.global.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        else if (activeTab === 'char-regex') {\n            appState.regexes.character.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        return visibleItems;\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        let filteredBookData = [];\n        if (!searchTerm) {\n            filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));\n        }\n        else {\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);\n                const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n                if (bookNameMatches || matchingEntries.length > 0) {\n                    filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });\n                }\n            });\n        }\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter) {\n            $container.html(`<p class=\"wio-info-text\">请先加载一个角色以管理角色世界书。</p>`);\n            return;\n        }\n        if (linkedBooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);\n            return;\n        }\n        const renderBook = (bookName) => {\n            const $bookContainer = $(`\r\n        <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n          <div class=\"wio-book-group-header\">\r\n            <span>${escapeHtml(bookName)}</span>\r\n            <div class=\"wio-item-controls\">\r\n              <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-entry-list-wrapper\"></div>\r\n        </div>\r\n      `);\n            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n            const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button><button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-shield-halved\"></i> 全开防递</button><button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-check-double\"></i> 修复关键词</button></div>`);\n            $listWrapper.append($entryActions);\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            const bookNameMatches = !searchTerm || (appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm));\n            const matchingEntries = entries.filter(entry => !searchTerm ||\n                (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n            if (!bookNameMatches && matchingEntries.length === 0)\n                return null;\n            const entriesToShow = bookNameMatches ? entries : matchingEntries;\n            if (entriesToShow.length === 0 && searchTerm) {\n                $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n            }\n            else {\n                entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n            }\n            return $bookContainer;\n        };\n        let renderedCount = 0;\n        linkedBooks.forEach(bookName => {\n            const $el = renderBook(bookName);\n            if ($el) {\n                $container.append($el);\n                renderedCount++;\n            }\n        });\n        if (renderedCount === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书或条目。</p>`);\n        }\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat) {\n            $container.html(`<p class=\"wio-info-text\">请先开始一个聊天以管理聊天世界书。</p>`);\n            return;\n        }\n        if (!bookName) {\n            $container.html(`\r\n        <div class=\"wio-info-section\">\r\n          <p class=\"wio-info-text\">当前聊天没有绑定世界书。</p>\r\n          <button id=\"wio-create-chat-lore-btn\" class=\"wio-btn wio-btn-primary\">\r\n            <i class=\"fa-solid fa-plus\"></i> 创建聊天世界书\r\n          </button>\r\n        </div>\r\n      `);\n            return;\n        }\n        const $bookContainer = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n        <div class=\"wio-book-group-header\">\r\n          <span>${escapeHtml(bookName)} (聊天世界书)</span>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-unlink-chat-lore-btn\" title=\"解除绑定\"><i class=\"fa-solid fa-unlink\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-entry-list-wrapper\"></div>\r\n      </div>\r\n    `);\n        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n        const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button></div>`);\n        $listWrapper.append($entryActions);\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const matchingEntries = entries.filter(entry => !searchTerm ||\n            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n        if (matchingEntries.length === 0 && searchTerm) {\n            $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n        }\n        else {\n            matchingEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n        }\n        $container.empty().append($bookContainer);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        if (regexes.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">没有找到${title}。</p>`);\n            return;\n        }\n        // 按启用状态和名称排序\n        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));\n        // 过滤匹配项\n        let filteredRegexes = sortedRegexes;\n        if (searchTerm) {\n            filteredRegexes = sortedRegexes.filter(regex => {\n                const name = regex.script_name || '';\n                const findRegex = regex.find_regex || '';\n                const replaceString = regex.replace_string || '';\n                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n        }\n        if (filteredRegexes.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的${title}。</p>`);\n            return;\n        }\n        // 添加操作按钮区域\n        const $actions = $(`\r\n      <div class=\"wio-regex-actions\">\r\n        <button class=\"wio-action-btn wio-create-regex-btn\" data-scope=\"${title === '全局正则' ? 'global' : 'character'}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-import-regex-btn\">\r\n          <i class=\"fa-solid fa-upload\"></i> 导入正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-export-regex-btn\">\r\n          <i class=\"fa-solid fa-download\"></i> 导出正则\r\n        </button>\r\n      </div>\r\n    `);\n        $container.append($actions);\n        // 渲染正则列表\n        const $regexList = $('<div class=\"wio-regex-list\"></div>');\n        filteredRegexes.forEach((regex, index) => {\n            const $element = createItemElement(regex, 'regex', '', searchTerm);\n            // 添加序号指示器\n            $element.find('.wio-item-name').prepend(`<span class=\"wio-order-indicator\">#${index + 1}</span> `);\n            $regexList.append($element);\n        });\n        $container.append($regexList);\n        // 初始化拖拽排序（仅对非搜索状态的完整列表）\n        if (!searchTerm && parentWin.Sortable) {\n            const listEl = $regexList[0];\n            if (listEl) {\n                new parentWin.Sortable(listEl, {\n                    animation: 150,\n                    handle: '.wio-drag-handle',\n                    ghostClass: 'sortable-ghost',\n                    chosenClass: 'sortable-chosen',\n                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),\n                });\n            }\n        }\n    };\n    // --- 核心UI元素创建函数 ---\n    const createItemElement = (item, type, bookName = '', searchTerm = '') => {\n        const isLore = type === 'lore';\n        const id = isLore ? item.uid : item.id;\n        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';\n        const fromCard = item.source === 'card';\n        let controlsHtml = '';\n        if (isLore) {\n            // 所有世界书条目都有完整的操作按钮\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n        <button class=\"wio-action-btn-icon wio-delete-entry-btn\" title=\"删除条目\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n      `;\n        }\n        else if (fromCard) {\n            // 来自卡片的正则只有开关\n            controlsHtml =\n                '<button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>';\n        }\n        else {\n            // UI中的正则有重命名和开关\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n      `;\n        }\n        const dragHandleHtml = !fromCard && !isLore\n            ? '<span class=\"wio-drag-handle\" title=\"拖拽排序\"><i class=\"fa-solid fa-grip-vertical\"></i></span>'\n            : '';\n        // 应用高亮到条目名称\n        const highlightedName = highlightText(name, searchTerm);\n        const $element = $(`<div class=\"wio-item-container ${fromCard ? 'from-card' : ''}\" data-type=\"${type}\" data-id=\"${id}\" ${isLore ? `data-book-name=\"${escapeHtml(bookName)}\"` : ''}><div class=\"wio-item-header\" title=\"${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}\">${dragHandleHtml}<span class=\"wio-item-name\">${highlightedName}</span><div class=\"wio-item-controls\">${controlsHtml}</div></div><div class=\"wio-collapsible-content\"></div></div>`);\n        // 保存搜索词以便在内容展开时使用\n        $element.data('searchTerm', searchTerm);\n        $element.toggleClass('enabled', item.enabled);\n        if (appState.multiSelectMode) {\n            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;\n            $element.toggleClass('selected', appState.selectedItems.has(itemKey));\n        }\n        return $element;\n    };\n    const createGlobalLorebookElement = (book, searchTerm, forceShowAllEntries, filteredEntries) => {\n        const usedByChars = appState.lorebookUsage.get(book.name) || [];\n        const usedByHtml = usedByChars.length > 0\n            ? `<div class=\"wio-used-by-chars\">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`\n            : '';\n        const $element = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(book.name)}\">\r\n        <div class=\"wio-global-book-header\" title=\"${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}\">\r\n          <div class=\"wio-book-info\">\r\n            <span class=\"wio-book-name\">${highlightText(book.name, searchTerm)}</span>\r\n            <span class=\"wio-book-status ${book.enabled ? 'enabled' : 'disabled'}\">${book.enabled ? '已启用' : '已禁用'}</span>\r\n            ${usedByHtml}\r\n          </div>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"编辑条目\"><i class=\"fa-solid fa-edit\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\"></div>\r\n      </div>\r\n    `);\n        const $content = $element.find('.wio-collapsible-content');\n        // 添加条目操作按钮\n        const $entryActions = $(`\r\n      <div class=\"wio-entry-actions\">\r\n        <button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建条目\r\n        </button>\r\n        <button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-shield-halved\"></i> 全开防递\r\n        </button>\r\n        <button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-check-double\"></i> 修复关键词\r\n        </button>\r\n      </div>\r\n    `);\n        $content.append($entryActions);\n        const allEntries = [...safeGetLorebookEntries(book.name)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const entriesToShow = forceShowAllEntries ? allEntries : filteredEntries || [];\n        if (entriesToShow && entriesToShow.length > 0) {\n            const $listWrapper = $('<div class=\"wio-entry-list-wrapper\"></div>');\n            entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));\n            $content.append($listWrapper);\n        }\n        else if (searchTerm) {\n            $content.append(`<div class=\"wio-info-text-small\">无匹配项</div>`);\n        }\n        return $element;\n    };\n    // --- 替换功能实现 ---\n    const handleReplace = errorCatched(async () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();\n        const replaceTerm = $('#wio-replace-input', parentWin.document).val();\n        // 检查搜索词是否为空\n        if (!searchTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });\n            return;\n        }\n        // 检查替换词是否为空\n        if (!replaceTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });\n            return;\n        }\n        // 获取当前视图的匹配项\n        let matches = [];\n        switch (appState.activeTab) {\n            case 'global-lore':\n                matches = getGlobalLorebookMatches(searchTerm);\n                break;\n            case 'char-lore':\n                matches = getCharacterLorebookMatches(searchTerm);\n                break;\n            case 'chat-lore':\n                matches = getChatLorebookMatches(searchTerm);\n                break;\n            default:\n                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });\n                return;\n        }\n        // 如果没有匹配项，提示用户\n        if (matches.length === 0) {\n            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });\n            return;\n        }\n        // 显示确认对话框\n        const confirmResult = await showModal({\n            type: 'confirm',\n            title: '确认替换',\n            text: `找到 ${matches.length} 个匹配项。\\n\\n确定要将 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\\n此操作不可撤销，请谨慎操作。`,\n        });\n        // 如果用户确认替换，则执行替换\n        if (confirmResult) {\n            const progressToast = showProgressToast('正在执行替换...');\n            try {\n                await performReplace(matches, searchTerm, replaceTerm);\n                progressToast.remove();\n                showSuccessTick('替换完成');\n                // 刷新视图\n                renderContent();\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Replace error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '替换失败',\n                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',\n                });\n            }\n        }\n    });\n    // 执行替换操作的函数\n    const performReplace = async (matches, searchTerm, replaceTerm) => {\n        // 创建一个映射来跟踪每个世界书的更改\n        const bookUpdates = new Map();\n        // 遍历所有匹配项\n        for (const match of matches) {\n            const { bookName, entry } = match;\n            let updated = false;\n            // 如果还没有为这个世界书创建更新数组，则创建一个\n            if (!bookUpdates.has(bookName)) {\n                bookUpdates.set(bookName, []);\n            }\n            // 创建条目的深拷贝以进行修改\n            const updatedEntry = JSON.parse(JSON.stringify(entry));\n            // 替换关键词\n            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {\n                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm));\n                // 检查是否有实际更改\n                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {\n                    updatedEntry.keys = newKeys;\n                    updated = true;\n                }\n            }\n            // 替换条目内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.content !== newContent) {\n                    updatedEntry.content = newContent;\n                    updated = true;\n                }\n            }\n            // 替换条目名称（comment）\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.comment !== newComment) {\n                    updatedEntry.comment = newComment;\n                    updated = true;\n                }\n            }\n            // 如果有更改，则将更新后的条目添加到更新数组中\n            if (updated) {\n                bookUpdates.get(bookName).push(updatedEntry);\n            }\n        }\n        // 应用所有更改\n        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {\n            if (entriesToUpdate.length > 0) {\n                // 调用TavernAPI来更新条目\n                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);\n                if (result && result.entries) {\n                    // 更新本地状态\n                    safeSetLorebookEntries(bookName, result.entries);\n                }\n            }\n        }\n        // 等待一段时间以确保所有操作完成\n        await new Promise(resolve => setTimeout(resolve, 100));\n    };\n    // 获取匹配项的函数\n    const getGlobalLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                entries.forEach(entry => {\n                    matches.push({ bookName: book.name, entry });\n                });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm.toLowerCase());\n                entries.forEach(entry => {\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName: book.name, entry });\n                    }\n                });\n            });\n        }\n        return matches;\n    };\n    const getCharacterLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter || linkedBooks.length === 0) {\n            return matches;\n        }\n        linkedBooks.forEach(bookName => {\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (!searchTerm) {\n                // 如果没有搜索词，返回所有条目\n                entries.forEach(entry => {\n                    matches.push({ bookName, entry });\n                });\n            }\n            else {\n                // 根据搜索词和过滤器获取匹配项\n                entries.forEach(entry => {\n                    const bookNameMatches = appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm.toLowerCase());\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName, entry });\n                    }\n                });\n            }\n        });\n        return matches;\n    };\n    const getChatLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat || !bookName) {\n            return matches;\n        }\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            entries.forEach(entry => {\n                matches.push({ bookName, entry });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            entries.forEach(entry => {\n                const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                const contentMatch = appState.searchFilters.content &&\n                    entry.content &&\n                    entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                if (entryNameMatches || keywordsMatch || contentMatch) {\n                    matches.push({ bookName, entry });\n                }\n            });\n        }\n        return matches;\n    };\n    // --- SortableJS 加载和拖拽排序功能 ---\n    const loadSortableJS = (callback) => {\n        if (parentWin.Sortable) {\n            callback();\n            return;\n        }\n        const script = parentWin.document.createElement('script');\n        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';\n        script.onload = () => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');\n            callback();\n        };\n        script.onerror = () => {\n            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');\n            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });\n        };\n        parentWin.document.head.appendChild(script);\n    };\n    // 防抖函数\n    const debounce = (func, delay) => {\n        let timeout;\n        return (...args) => {\n            clearTimeout(timeout);\n            timeout = setTimeout(() => func(...args), delay);\n        };\n    };\n    // 防抖保存正则顺序\n    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {\n        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];\n        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));\n        await TavernAPI.saveSettings();\n        showSuccessTick('正则顺序已保存');\n    }), 800);\n    // 处理正则拖拽结束事件\n    const handleRegexDragEnd = errorCatched(async (evt, scope) => {\n        const { oldIndex, newIndex } = evt;\n        if (oldIndex === newIndex)\n            return;\n        const targetList = appState.regexes[scope];\n        const [movedItem] = targetList.splice(oldIndex, 1);\n        targetList.splice(newIndex, 0, movedItem);\n        // 乐观更新UI：重新渲染序号\n        renderContent();\n        // 防抖保存\n        debouncedSaveRegexOrder();\n    });\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 加载 SortableJS 然后初始化 UI\n        loadSortableJS(() => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');\n            // 创建主面板\n            createMainPanel();\n            // 创建扩展菜单按钮\n            createExtensionButton();\n            // 绑定事件处理器\n            bindEventHandlers();\n            // 加载初始数据\n            loadAllData();\n            console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n        });\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <input type=\"text\" id=\"wio-replace-input\" placeholder=\"替换为...\" class=\"wio-search-input\">\r\n                            <button id=\"wio-replace-btn\" class=\"wio-btn wio-search-btn\" title=\"替换\">\r\n                                <i class=\"fa-solid fa-exchange-alt\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                        <div id=\"wio-multi-select-controls\" class=\"wio-multi-select-controls\" style=\"display: none;\">\r\n                            <div class=\"wio-multi-select-actions\">\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-all-btn\">全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-none-btn\">取消全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-invert-btn\">反选</button>\r\n                                <button class=\"wio-multi-select-action-btn enable\" id=\"wio-batch-enable-btn\">批量启用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-disable-btn\">批量禁用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-delete-btn\">批量删除</button>\r\n                                <span class=\"wio-selection-count\" id=\"wio-selection-count\">已选择: 0</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5\" data-i18n=\"[title]${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                .wio-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 90%;\r\n                    max-width: 1200px;\r\n                    height: 80%;\r\n                    background: #2a2a2a;\r\n                    border: 1px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);\r\n                    z-index: 10000;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n                .wio-item-container {\r\n                    margin-bottom: 10px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                }\r\n                .wio-item-container.enabled {\r\n                    border-left: 3px solid #28a745;\r\n                }\r\n                .wio-item-container.selected {\r\n                    background: #2a4a6b;\r\n                    border-color: #007bff;\r\n                }\r\n                .wio-item-header {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    padding: 10px 15px;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-item-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-item-name {\r\n                    flex: 1;\r\n                    margin-left: 10px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-item-controls {\r\n                    display: flex;\r\n                    gap: 5px;\r\n                }\r\n                .wio-action-btn-icon {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn-icon:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-toggle-btn {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #dc3545;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-toggle-btn:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-book-group {\r\n                    margin-bottom: 20px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 6px;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-book-group-header, .wio-global-book-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px 6px 0 0;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-book-group-header:hover, .wio-global-book-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-book-name {\r\n                    font-size: 16px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status {\r\n                    margin-left: 10px;\r\n                    padding: 2px 8px;\r\n                    border-radius: 12px;\r\n                    font-size: 12px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-book-status.enabled {\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status.disabled {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-entry-actions, .wio-regex-actions {\r\n                    padding: 15px;\r\n                    border-bottom: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-action-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-multi-select-controls {\r\n                    margin-top: 10px;\r\n                    padding: 10px;\r\n                    background: #333;\r\n                    border-radius: 4px;\r\n                }\r\n                .wio-multi-select-actions {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                    align-items: center;\r\n                }\r\n                .wio-multi-select-action-btn {\r\n                    padding: 6px 12px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-multi-select-action-btn:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-multi-select-action-btn.enable {\r\n                    background: #28a745;\r\n                }\r\n                .wio-multi-select-action-btn.enable:hover {\r\n                    background: #218838;\r\n                }\r\n                .wio-multi-select-action-btn.disable {\r\n                    background: #dc3545;\r\n                }\r\n                .wio-multi-select-action-btn.disable:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-selection-count {\r\n                    margin-left: auto;\r\n                    color: #ccc;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-info-text-small {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 20px 0;\r\n                    font-size: 14px;\r\n                }\r\n                .wio-used-by-chars {\r\n                    margin-top: 5px;\r\n                    font-size: 12px;\r\n                    color: #aaa;\r\n                }\r\n                .wio-used-by-chars span {\r\n                    background: #555;\r\n                    padding: 2px 6px;\r\n                    border-radius: 3px;\r\n                    margin-right: 5px;\r\n                }\r\n                .wio-toast-notification {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-toast-notification.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-progress-toast {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-progress-toast.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-modal-overlay {\r\n                    position: fixed;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    background: rgba(0,0,0,0.7);\r\n                    z-index: 10002;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                }\r\n                .wio-modal-content {\r\n                    background: #2a2a2a;\r\n                    border-radius: 8px;\r\n                    max-width: 500px;\r\n                    width: 90%;\r\n                    max-height: 80vh;\r\n                    overflow-y: auto;\r\n                }\r\n                .wio-modal-header {\r\n                    padding: 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-body {\r\n                    padding: 20px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-modal-input {\r\n                    width: 100%;\r\n                    padding: 10px;\r\n                    margin-top: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-input.wio-input-error {\r\n                    border-color: #dc3545;\r\n                    animation: shake 0.5s;\r\n                }\r\n                @keyframes shake {\r\n                    0%, 100% { transform: translateX(0); }\r\n                    25% { transform: translateX(-5px); }\r\n                    75% { transform: translateX(5px); }\r\n                }\r\n                .wio-modal-footer {\r\n                    padding: 20px;\r\n                    border-top: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-modal-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-modal-ok {\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-ok:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-modal-cancel {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-cancel:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-drag-handle {\r\n                    cursor: grab;\r\n                    color: #ccc;\r\n                    margin-right: 10px;\r\n                    padding: 0 5px;\r\n                    opacity: 0.6;\r\n                    transition: opacity 0.2s;\r\n                }\r\n                .wio-drag-handle:hover {\r\n                    opacity: 1;\r\n                }\r\n                .wio-drag-handle:active {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-item-container.sortable-ghost {\r\n                    opacity: 0.4;\r\n                    background: #2a4a6b;\r\n                }\r\n                .wio-item-container.sortable-chosen {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-order-indicator {\r\n                    display: inline-block;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    font-size: 10px;\r\n                    font-weight: bold;\r\n                    padding: 2px 6px;\r\n                    border-radius: 10px;\r\n                    margin-right: 8px;\r\n                    min-width: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-regex-list {\r\n                    padding: 15px;\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                $panel.hide();\n            }\n            else {\n                $panel.show();\n                if (!appState.isDataLoaded) {\n                    loadAllData();\n                }\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            $(`#${PANEL_ID}`, parentDoc).hide();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {\n            const $this = $(event.currentTarget);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 替换按钮\n        $(parentDoc).on('click', '#wio-replace-btn', () => {\n            handleReplace();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            const $this = $(event.currentTarget);\n            if (appState.multiSelectMode) {\n                $this.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n            }\n            else {\n                $this.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    $panel.hide();\n                }\n            }\n        });\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();