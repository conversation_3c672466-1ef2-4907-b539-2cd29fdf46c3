var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!searchTerm || !text)\n            return escapeHtml(text);\n        const escapedText = escapeHtml(text);\n        const htmlSafeSearchTerm = escapeHtml(searchTerm);\n        const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n        return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    let TavernAPI = null;\n    const initializeTavernAPI = () => {\n        TavernAPI = {\n            createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n            deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n            getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n            setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n            getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n            Character: TavernHelper.Character || null,\n            getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n            replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n            getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n            getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n            getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n            getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n            getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n            setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n            getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n            setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n            createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n            deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n            saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n            setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n        };\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html('<p class=\"wio-info-text\">正在加载所有数据，请稍候...</p>');\n        try {\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch((error) => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    await Promise.all(allCharacters.map(async (char) => {\n                        if (!char || !char.name)\n                            return;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                if (result && typeof result.then === 'function') {\n                                    books = await result;\n                                }\n                                else {\n                                    books = result;\n                                }\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                                books = null;\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                        console.log(`[WorldInfoOptimizer] Character \"${char.name}\" uses lorebook \"${bookName}\"`);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                    }));\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 分批加载世界书条目，避免同时加载过多\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n            }\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(appState.multiSelectMode);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 选择和批量操作函数 ---\n    const updateSelectionCount = () => {\n        $(`#wio-selection-count`, parentWin.document).text(`已选择: ${appState.selectedItems.size}`);\n    };\n    const getAllVisibleItems = () => {\n        const visibleItems = [];\n        const activeTab = appState.activeTab;\n        if (activeTab === 'global-lore') {\n            appState.allLorebooks.forEach(book => {\n                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });\n                [...safeGetLorebookEntries(book.name)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'char-lore') {\n            appState.lorebooks.character.forEach(bookName => {\n                [...safeGetLorebookEntries(bookName)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'global-regex') {\n            appState.regexes.global.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        else if (activeTab === 'char-regex') {\n            appState.regexes.character.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        return visibleItems;\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        let filteredBookData = [];\n        if (!searchTerm) {\n            filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));\n        }\n        else {\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);\n                const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n                if (bookNameMatches || matchingEntries.length > 0) {\n                    filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });\n                }\n            });\n        }\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter) {\n            $container.html(`<p class=\"wio-info-text\">请先加载一个角色以管理角色世界书。</p>`);\n            return;\n        }\n        if (linkedBooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);\n            return;\n        }\n        const renderBook = (bookName) => {\n            const $bookContainer = $(`\r\n        <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n          <div class=\"wio-book-group-header\">\r\n            <span>${escapeHtml(bookName)}</span>\r\n            <div class=\"wio-item-controls\">\r\n              <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-entry-list-wrapper\"></div>\r\n        </div>\r\n      `);\n            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n            const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button><button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-shield-halved\"></i> 全开防递</button><button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-check-double\"></i> 修复关键词</button></div>`);\n            $listWrapper.append($entryActions);\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            const bookNameMatches = !searchTerm || (appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm));\n            const matchingEntries = entries.filter(entry => !searchTerm ||\n                (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n            if (!bookNameMatches && matchingEntries.length === 0)\n                return null;\n            const entriesToShow = bookNameMatches ? entries : matchingEntries;\n            if (entriesToShow.length === 0 && searchTerm) {\n                $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n            }\n            else {\n                entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n            }\n            return $bookContainer;\n        };\n        let renderedCount = 0;\n        linkedBooks.forEach(bookName => {\n            const $el = renderBook(bookName);\n            if ($el) {\n                $container.append($el);\n                renderedCount++;\n            }\n        });\n        if (renderedCount === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书或条目。</p>`);\n        }\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat) {\n            $container.html(`<p class=\"wio-info-text\">请先开始一个聊天以管理聊天世界书。</p>`);\n            return;\n        }\n        if (!bookName) {\n            $container.html(`\r\n        <div class=\"wio-info-section\">\r\n          <p class=\"wio-info-text\">当前聊天没有绑定世界书。</p>\r\n          <button id=\"wio-create-chat-lore-btn\" class=\"wio-btn wio-btn-primary\">\r\n            <i class=\"fa-solid fa-plus\"></i> 创建聊天世界书\r\n          </button>\r\n        </div>\r\n      `);\n            return;\n        }\n        const $bookContainer = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n        <div class=\"wio-book-group-header\">\r\n          <span>${escapeHtml(bookName)} (聊天世界书)</span>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-unlink-chat-lore-btn\" title=\"解除绑定\"><i class=\"fa-solid fa-unlink\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-entry-list-wrapper\"></div>\r\n      </div>\r\n    `);\n        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n        const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button></div>`);\n        $listWrapper.append($entryActions);\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const matchingEntries = entries.filter(entry => !searchTerm ||\n            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n        if (matchingEntries.length === 0 && searchTerm) {\n            $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n        }\n        else {\n            matchingEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n        }\n        $container.empty().append($bookContainer);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        if (regexes.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">没有找到${title}。</p>`);\n            return;\n        }\n        // 按启用状态和名称排序\n        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));\n        // 过滤匹配项\n        let filteredRegexes = sortedRegexes;\n        if (searchTerm) {\n            filteredRegexes = sortedRegexes.filter(regex => {\n                const name = regex.script_name || '';\n                const findRegex = regex.find_regex || '';\n                const replaceString = regex.replace_string || '';\n                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n        }\n        if (filteredRegexes.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的${title}。</p>`);\n            return;\n        }\n        // 添加操作按钮区域\n        const $actions = $(`\r\n      <div class=\"wio-regex-actions\">\r\n        <button class=\"wio-action-btn wio-create-regex-btn\" data-scope=\"${title === '全局正则' ? 'global' : 'character'}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-import-regex-btn\">\r\n          <i class=\"fa-solid fa-upload\"></i> 导入正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-export-regex-btn\">\r\n          <i class=\"fa-solid fa-download\"></i> 导出正则\r\n        </button>\r\n      </div>\r\n    `);\n        $container.append($actions);\n        // 渲染正则列表\n        const $regexList = $('<div class=\"wio-regex-list\"></div>');\n        filteredRegexes.forEach((regex, index) => {\n            const $element = createItemElement(regex, 'regex', '', searchTerm);\n            // 添加序号指示器\n            $element.find('.wio-item-name').prepend(`<span class=\"wio-order-indicator\">#${index + 1}</span> `);\n            $regexList.append($element);\n        });\n        $container.append($regexList);\n        // 初始化拖拽排序（仅对非搜索状态的完整列表）\n        if (!searchTerm && parentWin.Sortable) {\n            const listEl = $regexList[0];\n            if (listEl) {\n                new parentWin.Sortable(listEl, {\n                    animation: 150,\n                    handle: '.wio-drag-handle',\n                    ghostClass: 'sortable-ghost',\n                    chosenClass: 'sortable-chosen',\n                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),\n                });\n            }\n        }\n    };\n    // --- 核心UI元素创建函数 ---\n    const createItemElement = (item, type, bookName = '', searchTerm = '') => {\n        const isLore = type === 'lore';\n        const id = isLore ? item.uid : item.id;\n        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';\n        const fromCard = item.source === 'card';\n        let controlsHtml = '';\n        if (isLore) {\n            // 所有世界书条目都有完整的操作按钮\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n        <button class=\"wio-action-btn-icon wio-delete-entry-btn\" title=\"删除条目\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n      `;\n        }\n        else if (fromCard) {\n            // 来自卡片的正则只有开关\n            controlsHtml =\n                '<button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>';\n        }\n        else {\n            // UI中的正则有重命名和开关\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n      `;\n        }\n        const dragHandleHtml = !fromCard && !isLore\n            ? '<span class=\"wio-drag-handle\" title=\"拖拽排序\"><i class=\"fa-solid fa-grip-vertical\"></i></span>'\n            : '';\n        // 应用高亮到条目名称\n        const highlightedName = highlightText(name, searchTerm);\n        const $element = $(`<div class=\"wio-item-container ${fromCard ? 'from-card' : ''}\" data-type=\"${type}\" data-id=\"${id}\" ${isLore ? `data-book-name=\"${escapeHtml(bookName)}\"` : ''}><div class=\"wio-item-header\" title=\"${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}\">${dragHandleHtml}<span class=\"wio-item-name\">${highlightedName}</span><div class=\"wio-item-controls\">${controlsHtml}</div></div><div class=\"wio-collapsible-content\"></div></div>`);\n        // 保存搜索词以便在内容展开时使用\n        $element.data('searchTerm', searchTerm);\n        $element.toggleClass('enabled', item.enabled);\n        if (appState.multiSelectMode) {\n            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;\n            $element.toggleClass('selected', appState.selectedItems.has(itemKey));\n        }\n        return $element;\n    };\n    const createGlobalLorebookElement = (book, searchTerm, forceShowAllEntries, filteredEntries) => {\n        const usedByChars = appState.lorebookUsage.get(book.name) || [];\n        const usedByHtml = usedByChars.length > 0\n            ? `<div class=\"wio-used-by-chars\">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`\n            : '';\n        const $element = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(book.name)}\">\r\n        <div class=\"wio-global-book-header\" title=\"${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}\">\r\n          <div class=\"wio-book-info\">\r\n            <span class=\"wio-book-name\">${highlightText(book.name, searchTerm)}</span>\r\n            <span class=\"wio-book-status ${book.enabled ? 'enabled' : 'disabled'}\">${book.enabled ? '已启用' : '已禁用'}</span>\r\n            ${usedByHtml}\r\n          </div>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"编辑条目\"><i class=\"fa-solid fa-edit\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\"></div>\r\n      </div>\r\n    `);\n        const $content = $element.find('.wio-collapsible-content');\n        // 添加条目操作按钮\n        const $entryActions = $(`\r\n      <div class=\"wio-entry-actions\">\r\n        <button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建条目\r\n        </button>\r\n        <button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-shield-halved\"></i> 全开防递\r\n        </button>\r\n        <button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-check-double\"></i> 修复关键词\r\n        </button>\r\n      </div>\r\n    `);\n        $content.append($entryActions);\n        const allEntries = [...safeGetLorebookEntries(book.name)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const entriesToShow = forceShowAllEntries ? allEntries : filteredEntries || [];\n        if (entriesToShow && entriesToShow.length > 0) {\n            const $listWrapper = $('<div class=\"wio-entry-list-wrapper\"></div>');\n            entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));\n            $content.append($listWrapper);\n        }\n        else if (searchTerm) {\n            $content.append(`<div class=\"wio-info-text-small\">无匹配项</div>`);\n        }\n        return $element;\n    };\n    // --- 替换功能实现 ---\n    const handleReplace = errorCatched(async () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();\n        const replaceTerm = $('#wio-replace-input', parentWin.document).val();\n        // 检查搜索词是否为空\n        if (!searchTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });\n            return;\n        }\n        // 检查替换词是否为空\n        if (!replaceTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });\n            return;\n        }\n        // 获取当前视图的匹配项\n        let matches = [];\n        switch (appState.activeTab) {\n            case 'global-lore':\n                matches = getGlobalLorebookMatches(searchTerm);\n                break;\n            case 'char-lore':\n                matches = getCharacterLorebookMatches(searchTerm);\n                break;\n            case 'chat-lore':\n                matches = getChatLorebookMatches(searchTerm);\n                break;\n            default:\n                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });\n                return;\n        }\n        // 如果没有匹配项，提示用户\n        if (matches.length === 0) {\n            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });\n            return;\n        }\n        // 显示确认对话框\n        const confirmResult = await showModal({\n            type: 'confirm',\n            title: '确认替换',\n            text: `找到 ${matches.length} 个匹配项。\\n\\n确定要将 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\\n此操作不可撤销，请谨慎操作。`,\n        });\n        // 如果用户确认替换，则执行替换\n        if (confirmResult) {\n            const progressToast = showProgressToast('正在执行替换...');\n            try {\n                await performReplace(matches, searchTerm, replaceTerm);\n                progressToast.remove();\n                showSuccessTick('替换完成');\n                // 刷新视图\n                renderContent();\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Replace error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '替换失败',\n                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',\n                });\n            }\n        }\n    });\n    // 执行替换操作的函数\n    const performReplace = async (matches, searchTerm, replaceTerm) => {\n        // 创建一个映射来跟踪每个世界书的更改\n        const bookUpdates = new Map();\n        // 遍历所有匹配项\n        for (const match of matches) {\n            const { bookName, entry } = match;\n            let updated = false;\n            // 如果还没有为这个世界书创建更新数组，则创建一个\n            if (!bookUpdates.has(bookName)) {\n                bookUpdates.set(bookName, []);\n            }\n            // 创建条目的深拷贝以进行修改\n            const updatedEntry = JSON.parse(JSON.stringify(entry));\n            // 替换关键词\n            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {\n                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm));\n                // 检查是否有实际更改\n                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {\n                    updatedEntry.keys = newKeys;\n                    updated = true;\n                }\n            }\n            // 替换条目内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.content !== newContent) {\n                    updatedEntry.content = newContent;\n                    updated = true;\n                }\n            }\n            // 替换条目名称（comment）\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.comment !== newComment) {\n                    updatedEntry.comment = newComment;\n                    updated = true;\n                }\n            }\n            // 如果有更改，则将更新后的条目添加到更新数组中\n            if (updated) {\n                bookUpdates.get(bookName).push(updatedEntry);\n            }\n        }\n        // 应用所有更改\n        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {\n            if (entriesToUpdate.length > 0) {\n                // 调用TavernAPI来更新条目\n                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);\n                if (result && result.entries) {\n                    // 更新本地状态\n                    safeSetLorebookEntries(bookName, result.entries);\n                }\n            }\n        }\n        // 等待一段时间以确保所有操作完成\n        await new Promise(resolve => setTimeout(resolve, 100));\n    };\n    // 获取匹配项的函数\n    const getGlobalLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                entries.forEach(entry => {\n                    matches.push({ bookName: book.name, entry });\n                });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm.toLowerCase());\n                entries.forEach(entry => {\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName: book.name, entry });\n                    }\n                });\n            });\n        }\n        return matches;\n    };\n    const getCharacterLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter || linkedBooks.length === 0) {\n            return matches;\n        }\n        linkedBooks.forEach(bookName => {\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (!searchTerm) {\n                // 如果没有搜索词，返回所有条目\n                entries.forEach(entry => {\n                    matches.push({ bookName, entry });\n                });\n            }\n            else {\n                // 根据搜索词和过滤器获取匹配项\n                entries.forEach(entry => {\n                    const bookNameMatches = appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm.toLowerCase());\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName, entry });\n                    }\n                });\n            }\n        });\n        return matches;\n    };\n    const getChatLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat || !bookName) {\n            return matches;\n        }\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            entries.forEach(entry => {\n                matches.push({ bookName, entry });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            entries.forEach(entry => {\n                const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                const contentMatch = appState.searchFilters.content &&\n                    entry.content &&\n                    entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                if (entryNameMatches || keywordsMatch || contentMatch) {\n                    matches.push({ bookName, entry });\n                }\n            });\n        }\n        return matches;\n    };\n    // --- SortableJS 加载和拖拽排序功能 ---\n    const loadSortableJS = (callback) => {\n        if (parentWin.Sortable) {\n            callback();\n            return;\n        }\n        const script = parentWin.document.createElement('script');\n        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';\n        script.onload = () => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');\n            callback();\n        };\n        script.onerror = () => {\n            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');\n            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });\n        };\n        parentWin.document.head.appendChild(script);\n    };\n    // 防抖函数\n    const debounce = (func, delay) => {\n        let timeout;\n        return (...args) => {\n            clearTimeout(timeout);\n            timeout = setTimeout(() => func(...args), delay);\n        };\n    };\n    // 防抖保存正则顺序\n    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {\n        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];\n        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));\n        await TavernAPI.saveSettings();\n        showSuccessTick('正则顺序已保存');\n    }), 800);\n    // 处理正则拖拽结束事件\n    const handleRegexDragEnd = errorCatched(async (evt, scope) => {\n        const { oldIndex, newIndex } = evt;\n        if (oldIndex === newIndex)\n            return;\n        const targetList = appState.regexes[scope];\n        const [movedItem] = targetList.splice(oldIndex, 1);\n        targetList.splice(newIndex, 0, movedItem);\n        // 乐观更新UI：重新渲染序号\n        renderContent();\n        // 防抖保存\n        debouncedSaveRegexOrder();\n    });\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 初始化 TavernAPI\n        initializeTavernAPI();\n        // 加载 SortableJS 然后初始化 UI\n        loadSortableJS(() => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');\n            // 创建主面板\n            createMainPanel();\n            // 创建扩展菜单按钮\n            createExtensionButton();\n            // 绑定事件处理器\n            bindEventHandlers();\n            // 加载初始数据\n            loadAllData();\n            console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n        });\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <input type=\"text\" id=\"wio-replace-input\" placeholder=\"替换为...\" class=\"wio-search-input\">\r\n                            <button id=\"wio-replace-btn\" class=\"wio-btn wio-search-btn\" title=\"替换\">\r\n                                <i class=\"fa-solid fa-exchange-alt\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                        <div id=\"wio-multi-select-controls\" class=\"wio-multi-select-controls\" style=\"display: none;\">\r\n                            <div class=\"wio-multi-select-actions\">\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-all-btn\">全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-none-btn\">取消全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-invert-btn\">反选</button>\r\n                                <button class=\"wio-multi-select-action-btn enable\" id=\"wio-batch-enable-btn\">批量启用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-disable-btn\">批量禁用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-delete-btn\">批量删除</button>\r\n                                <span class=\"wio-selection-count\" id=\"wio-selection-count\">已选择: 0</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5 interactable\" title=\"${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n            console.log(`[WorldInfoOptimizer] Button #${BUTTON_ID} appended to #extensionsMenu.`);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                .wio-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 90%;\r\n                    max-width: 1200px;\r\n                    height: 80%;\r\n                    background: #2a2a2a;\r\n                    border: 1px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);\r\n                    z-index: 10000;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n                .wio-item-container {\r\n                    margin-bottom: 10px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                }\r\n                .wio-item-container.enabled {\r\n                    border-left: 3px solid #28a745;\r\n                }\r\n                .wio-item-container.selected {\r\n                    background: #2a4a6b;\r\n                    border-color: #007bff;\r\n                }\r\n                .wio-item-header {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    padding: 10px 15px;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-item-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-item-name {\r\n                    flex: 1;\r\n                    margin-left: 10px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-item-controls {\r\n                    display: flex;\r\n                    gap: 5px;\r\n                }\r\n                .wio-action-btn-icon {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn-icon:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-toggle-btn {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #dc3545;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-toggle-btn:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-book-group {\r\n                    margin-bottom: 20px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 6px;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-book-group-header, .wio-global-book-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px 6px 0 0;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-book-group-header:hover, .wio-global-book-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-book-name {\r\n                    font-size: 16px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status {\r\n                    margin-left: 10px;\r\n                    padding: 2px 8px;\r\n                    border-radius: 12px;\r\n                    font-size: 12px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-book-status.enabled {\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status.disabled {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-entry-actions, .wio-regex-actions {\r\n                    padding: 15px;\r\n                    border-bottom: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-action-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-multi-select-controls {\r\n                    margin-top: 10px;\r\n                    padding: 10px;\r\n                    background: #333;\r\n                    border-radius: 4px;\r\n                }\r\n                .wio-multi-select-actions {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                    align-items: center;\r\n                }\r\n                .wio-multi-select-action-btn {\r\n                    padding: 6px 12px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-multi-select-action-btn:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-multi-select-action-btn.enable {\r\n                    background: #28a745;\r\n                }\r\n                .wio-multi-select-action-btn.enable:hover {\r\n                    background: #218838;\r\n                }\r\n                .wio-multi-select-action-btn.disable {\r\n                    background: #dc3545;\r\n                }\r\n                .wio-multi-select-action-btn.disable:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-selection-count {\r\n                    margin-left: auto;\r\n                    color: #ccc;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-info-text-small {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 20px 0;\r\n                    font-size: 14px;\r\n                }\r\n                .wio-used-by-chars {\r\n                    margin-top: 5px;\r\n                    font-size: 12px;\r\n                    color: #aaa;\r\n                }\r\n                .wio-used-by-chars span {\r\n                    background: #555;\r\n                    padding: 2px 6px;\r\n                    border-radius: 3px;\r\n                    margin-right: 5px;\r\n                }\r\n                .wio-toast-notification {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-toast-notification.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-progress-toast {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-progress-toast.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-modal-overlay {\r\n                    position: fixed;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    background: rgba(0,0,0,0.7);\r\n                    z-index: 10002;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                }\r\n                .wio-modal-content {\r\n                    background: #2a2a2a;\r\n                    border-radius: 8px;\r\n                    max-width: 500px;\r\n                    width: 90%;\r\n                    max-height: 80vh;\r\n                    overflow-y: auto;\r\n                }\r\n                .wio-modal-header {\r\n                    padding: 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-body {\r\n                    padding: 20px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-modal-input {\r\n                    width: 100%;\r\n                    padding: 10px;\r\n                    margin-top: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-input.wio-input-error {\r\n                    border-color: #dc3545;\r\n                    animation: shake 0.5s;\r\n                }\r\n                @keyframes shake {\r\n                    0%, 100% { transform: translateX(0); }\r\n                    25% { transform: translateX(-5px); }\r\n                    75% { transform: translateX(5px); }\r\n                }\r\n                .wio-modal-footer {\r\n                    padding: 20px;\r\n                    border-top: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-modal-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-modal-ok {\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-ok:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-modal-cancel {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-cancel:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-drag-handle {\r\n                    cursor: grab;\r\n                    color: #ccc;\r\n                    margin-right: 10px;\r\n                    padding: 0 5px;\r\n                    opacity: 0.6;\r\n                    transition: opacity 0.2s;\r\n                }\r\n                .wio-drag-handle:hover {\r\n                    opacity: 1;\r\n                }\r\n                .wio-drag-handle:active {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-item-container.sortable-ghost {\r\n                    opacity: 0.4;\r\n                    background: #2a4a6b;\r\n                }\r\n                .wio-item-container.sortable-chosen {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-order-indicator {\r\n                    display: inline-block;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    font-size: 10px;\r\n                    font-weight: bold;\r\n                    padding: 2px 6px;\r\n                    border-radius: 10px;\r\n                    margin-right: 8px;\r\n                    min-width: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-regex-list {\r\n                    padding: 15px;\r\n                }\r\n                /* 按钮激活状态 */\r\n                #${BUTTON_ID}.active {\r\n                    background-color: rgba(126, 183, 213, 0.3) !important;\r\n                    border-color: #7eb7d5 !important;\r\n                }\r\n                #${BUTTON_ID}:hover {\r\n                    background-color: rgba(126, 183, 213, 0.15);\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 面板显示/隐藏函数 ---\n    const hidePanel = () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.hide();\n        $(`#${BUTTON_ID}`, parentDoc).removeClass('active');\n        $parentBody.off('mousedown.wio-outside-click');\n    };\n    const showPanel = async () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.css('display', 'flex');\n        $(`#${BUTTON_ID}`, parentDoc).addClass('active');\n        // 点击外部关闭面板\n        $parentBody.on('mousedown.wio-outside-click', function (event) {\n            if ($(event.target).closest(`#${PANEL_ID}`).length === 0 &&\n                $(event.target).closest(`#${BUTTON_ID}`).length === 0) {\n                hidePanel();\n            }\n        });\n        if (!appState.isDataLoaded) {\n            await loadAllData();\n        }\n        else {\n            renderContent();\n        }\n    };\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                hidePanel();\n            }\n            else {\n                await showPanel();\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            hidePanel();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {\n            const $this = $(event.currentTarget);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 替换按钮\n        $(parentDoc).on('click', '#wio-replace-btn', () => {\n            handleReplace();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            const $this = $(event.currentTarget);\n            if (appState.multiSelectMode) {\n                $this.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n            }\n            else {\n                $this.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    $panel.hide();\n                }\n            }\n        });\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();