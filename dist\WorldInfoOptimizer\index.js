var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!searchTerm || !text)\n            return escapeHtml(text);\n        const escapedText = escapeHtml(text);\n        const htmlSafeSearchTerm = escapeHtml(searchTerm);\n        const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n        return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    const TavernAPI = {\n        createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n        deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n        getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n        setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n        getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n        Character: TavernHelper.Character,\n        getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n        replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n        getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n        getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n        getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n        getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n        getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n        setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n        getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n        setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n        createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n        deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n        saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n        setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html('<p class=\"wio-info-text\">正在加载所有数据，请稍候...</p>');\n        try {\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch(error => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    await Promise.all(allCharacters.map(async (char) => {\n                        if (!char || !char.name)\n                            return;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                if (result && typeof result.then === 'function') {\n                                    books = await result;\n                                }\n                                else {\n                                    books = result;\n                                }\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                                books = null;\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                        console.log(`[WorldInfoOptimizer] Character \"${char.name}\" uses lorebook \"${bookName}\"`);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                    }));\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 分批加载世界书条目，避免同时加载过多\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n            }\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(appState.multiSelectMode);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 选择和批量操作函数 ---\n    const updateSelectionCount = () => {\n        $(`#wio-selection-count`, parentWin.document).text(`已选择: ${appState.selectedItems.size}`);\n    };\n    const getAllVisibleItems = () => {\n        const visibleItems = [];\n        const activeTab = appState.activeTab;\n        if (activeTab === 'global-lore') {\n            appState.allLorebooks.forEach(book => {\n                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });\n                [...safeGetLorebookEntries(book.name)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'char-lore') {\n            appState.lorebooks.character.forEach(bookName => {\n                [...safeGetLorebookEntries(bookName)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'global-regex') {\n            appState.regexes.global.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        else if (activeTab === 'char-regex') {\n            appState.regexes.character.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        return visibleItems;\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        let filteredBookData = [];\n        if (!searchTerm) {\n            filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));\n        }\n        else {\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);\n                const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n                if (bookNameMatches || matchingEntries.length > 0) {\n                    filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });\n                }\n            });\n        }\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter) {\n            $container.html(`<p class=\"wio-info-text\">请先加载一个角色以管理角色世界书。</p>`);\n            return;\n        }\n        if (linkedBooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);\n            return;\n        }\n        const renderBook = (bookName) => {\n            const $bookContainer = $(`\r\n        <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n          <div class=\"wio-book-group-header\">\r\n            <span>${escapeHtml(bookName)}</span>\r\n            <div class=\"wio-item-controls\">\r\n              <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-entry-list-wrapper\"></div>\r\n        </div>\r\n      `);\n            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n            const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button><button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-shield-halved\"></i> 全开防递</button><button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-check-double\"></i> 修复关键词</button></div>`);\n            $listWrapper.append($entryActions);\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            const bookNameMatches = !searchTerm || (appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm));\n            const matchingEntries = entries.filter(entry => !searchTerm ||\n                (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n            if (!bookNameMatches && matchingEntries.length === 0)\n                return null;\n            const entriesToShow = bookNameMatches ? entries : matchingEntries;\n            if (entriesToShow.length === 0 && searchTerm) {\n                $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n            }\n            else {\n                entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n            }\n            return $bookContainer;\n        };\n        let renderedCount = 0;\n        linkedBooks.forEach(bookName => {\n            const $el = renderBook(bookName);\n            if ($el) {\n                $container.append($el);\n                renderedCount++;\n            }\n        });\n        if (renderedCount === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书或条目。</p>`);\n        }\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat) {\n            $container.html(`<p class=\"wio-info-text\">请先开始一个聊天以管理聊天世界书。</p>`);\n            return;\n        }\n        if (!bookName) {\n            $container.html(`\r\n        <div class=\"wio-info-section\">\r\n          <p class=\"wio-info-text\">当前聊天没有绑定世界书。</p>\r\n          <button id=\"wio-create-chat-lore-btn\" class=\"wio-btn wio-btn-primary\">\r\n            <i class=\"fa-solid fa-plus\"></i> 创建聊天世界书\r\n          </button>\r\n        </div>\r\n      `);\n            return;\n        }\n        const $bookContainer = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n        <div class=\"wio-book-group-header\">\r\n          <span>${escapeHtml(bookName)} (聊天世界书)</span>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-unlink-chat-lore-btn\" title=\"解除绑定\"><i class=\"fa-solid fa-unlink\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-entry-list-wrapper\"></div>\r\n      </div>\r\n    `);\n        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n        const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button></div>`);\n        $listWrapper.append($entryActions);\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const matchingEntries = entries.filter(entry => !searchTerm ||\n            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n        if (matchingEntries.length === 0 && searchTerm) {\n            $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n        }\n        else {\n            matchingEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n        }\n        $container.empty().append($bookContainer);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        if (regexes.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">没有找到${title}。</p>`);\n            return;\n        }\n        // 按启用状态和名称排序\n        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));\n        // 过滤匹配项\n        let filteredRegexes = sortedRegexes;\n        if (searchTerm) {\n            filteredRegexes = sortedRegexes.filter(regex => {\n                const name = regex.script_name || '';\n                const findRegex = regex.find_regex || '';\n                const replaceString = regex.replace_string || '';\n                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n        }\n        if (filteredRegexes.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的${title}。</p>`);\n            return;\n        }\n        // 添加操作按钮区域\n        const $actions = $(`\r\n      <div class=\"wio-regex-actions\">\r\n        <button class=\"wio-action-btn wio-create-regex-btn\" data-scope=\"${title === '全局正则' ? 'global' : 'character'}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-import-regex-btn\">\r\n          <i class=\"fa-solid fa-upload\"></i> 导入正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-export-regex-btn\">\r\n          <i class=\"fa-solid fa-download\"></i> 导出正则\r\n        </button>\r\n      </div>\r\n    `);\n        $container.append($actions);\n        // 渲染正则列表\n        const $regexList = $('<div class=\"wio-regex-list\"></div>');\n        filteredRegexes.forEach((regex, index) => {\n            const $element = createItemElement(regex, 'regex', '', searchTerm);\n            // 添加序号指示器\n            $element.find('.wio-item-name').prepend(`<span class=\"wio-order-indicator\">#${index + 1}</span> `);\n            $regexList.append($element);\n        });\n        $container.append($regexList);\n        // 初始化拖拽排序（仅对非搜索状态的完整列表）\n        if (!searchTerm && parentWin.Sortable) {\n            const listEl = $regexList[0];\n            if (listEl) {\n                new parentWin.Sortable(listEl, {\n                    animation: 150,\n                    handle: '.wio-drag-handle',\n                    ghostClass: 'sortable-ghost',\n                    chosenClass: 'sortable-chosen',\n                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),\n                });\n            }\n        }\n    };\n    // --- 核心UI元素创建函数 ---\n    const createItemElement = (item, type, bookName = '', searchTerm = '') => {\n        const isLore = type === 'lore';\n        const id = isLore ? item.uid : item.id;\n        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';\n        const fromCard = item.source === 'card';\n        let controlsHtml = '';\n        if (isLore) {\n            // 所有世界书条目都有完整的操作按钮\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n        <button class=\"wio-action-btn-icon wio-delete-entry-btn\" title=\"删除条目\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n      `;\n        }\n        else if (fromCard) {\n            // 来自卡片的正则只有开关\n            controlsHtml =\n                '<button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>';\n        }\n        else {\n            // UI中的正则有重命名和开关\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n      `;\n        }\n        const dragHandleHtml = !fromCard && !isLore\n            ? '<span class=\"wio-drag-handle\" title=\"拖拽排序\"><i class=\"fa-solid fa-grip-vertical\"></i></span>'\n            : '';\n        // 应用高亮到条目名称\n        const highlightedName = highlightText(name, searchTerm);\n        const $element = $(`<div class=\"wio-item-container ${fromCard ? 'from-card' : ''}\" data-type=\"${type}\" data-id=\"${id}\" ${isLore ? `data-book-name=\"${escapeHtml(bookName)}\"` : ''}><div class=\"wio-item-header\" title=\"${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}\">${dragHandleHtml}<span class=\"wio-item-name\">${highlightedName}</span><div class=\"wio-item-controls\">${controlsHtml}</div></div><div class=\"wio-collapsible-content\"></div></div>`);\n        // 保存搜索词以便在内容展开时使用\n        $element.data('searchTerm', searchTerm);\n        $element.toggleClass('enabled', item.enabled);\n        if (appState.multiSelectMode) {\n            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;\n            $element.toggleClass('selected', appState.selectedItems.has(itemKey));\n        }\n        return $element;\n    };\n    const createGlobalLorebookElement = (book, searchTerm, forceShowAllEntries, filteredEntries) => {\n        const usedByChars = appState.lorebookUsage.get(book.name) || [];\n        const usedByHtml = usedByChars.length > 0\n            ? `<div class=\"wio-used-by-chars\">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`\n            : '';\n        const $element = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(book.name)}\">\r\n        <div class=\"wio-global-book-header\" title=\"${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}\">\r\n          <div class=\"wio-book-info\">\r\n            <span class=\"wio-book-name\">${highlightText(book.name, searchTerm)}</span>\r\n            <span class=\"wio-book-status ${book.enabled ? 'enabled' : 'disabled'}\">${book.enabled ? '已启用' : '已禁用'}</span>\r\n            ${usedByHtml}\r\n          </div>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"编辑条目\"><i class=\"fa-solid fa-edit\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\"></div>\r\n      </div>\r\n    `);\n        const $content = $element.find('.wio-collapsible-content');\n        // 添加条目操作按钮\n        const $entryActions = $(`\r\n      <div class=\"wio-entry-actions\">\r\n        <button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建条目\r\n        </button>\r\n        <button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-shield-halved\"></i> 全开防递\r\n        </button>\r\n        <button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-check-double\"></i> 修复关键词\r\n        </button>\r\n      </div>\r\n    `);\n        $content.append($entryActions);\n        const allEntries = [...safeGetLorebookEntries(book.name)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const entriesToShow = forceShowAllEntries ? allEntries : filteredEntries || [];\n        if (entriesToShow && entriesToShow.length > 0) {\n            const $listWrapper = $('<div class=\"wio-entry-list-wrapper\"></div>');\n            entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));\n            $content.append($listWrapper);\n        }\n        else if (searchTerm) {\n            $content.append(`<div class=\"wio-info-text-small\">无匹配项</div>`);\n        }\n        return $element;\n    };\n    // --- 替换功能实现 ---\n    const handleReplace = errorCatched(async () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();\n        const replaceTerm = $('#wio-replace-input', parentWin.document).val();\n        // 检查搜索词是否为空\n        if (!searchTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });\n            return;\n        }\n        // 检查替换词是否为空\n        if (!replaceTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });\n            return;\n        }\n        // 获取当前视图的匹配项\n        let matches = [];\n        switch (appState.activeTab) {\n            case 'global-lore':\n                matches = getGlobalLorebookMatches(searchTerm);\n                break;\n            case 'char-lore':\n                matches = getCharacterLorebookMatches(searchTerm);\n                break;\n            case 'chat-lore':\n                matches = getChatLorebookMatches(searchTerm);\n                break;\n            default:\n                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });\n                return;\n        }\n        // 如果没有匹配项，提示用户\n        if (matches.length === 0) {\n            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });\n            return;\n        }\n        // 显示确认对话框\n        const confirmResult = await showModal({\n            type: 'confirm',\n            title: '确认替换',\n            text: `找到 ${matches.length} 个匹配项。\\n\\n确定要将 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\\n此操作不可撤销，请谨慎操作。`,\n        });\n        // 如果用户确认替换，则执行替换\n        if (confirmResult) {\n            const progressToast = showProgressToast('正在执行替换...');\n            try {\n                await performReplace(matches, searchTerm, replaceTerm);\n                progressToast.remove();\n                showSuccessTick('替换完成');\n                // 刷新视图\n                renderContent();\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Replace error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '替换失败',\n                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',\n                });\n            }\n        }\n    });\n    // 执行替换操作的函数\n    const performReplace = async (matches, searchTerm, replaceTerm) => {\n        // 创建一个映射来跟踪每个世界书的更改\n        const bookUpdates = new Map();\n        // 遍历所有匹配项\n        for (const match of matches) {\n            const { bookName, entry } = match;\n            let updated = false;\n            // 如果还没有为这个世界书创建更新数组，则创建一个\n            if (!bookUpdates.has(bookName)) {\n                bookUpdates.set(bookName, []);\n            }\n            // 创建条目的深拷贝以进行修改\n            const updatedEntry = JSON.parse(JSON.stringify(entry));\n            // 替换关键词\n            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {\n                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm));\n                // 检查是否有实际更改\n                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {\n                    updatedEntry.keys = newKeys;\n                    updated = true;\n                }\n            }\n            // 替换条目内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.content !== newContent) {\n                    updatedEntry.content = newContent;\n                    updated = true;\n                }\n            }\n            // 替换条目名称（comment）\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.comment !== newComment) {\n                    updatedEntry.comment = newComment;\n                    updated = true;\n                }\n            }\n            // 如果有更改，则将更新后的条目添加到更新数组中\n            if (updated) {\n                bookUpdates.get(bookName).push(updatedEntry);\n            }\n        }\n        // 应用所有更改\n        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {\n            if (entriesToUpdate.length > 0) {\n                // 调用TavernAPI来更新条目\n                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);\n                if (result && result.entries) {\n                    // 更新本地状态\n                    safeSetLorebookEntries(bookName, result.entries);\n                }\n            }\n        }\n        // 等待一段时间以确保所有操作完成\n        await new Promise(resolve => setTimeout(resolve, 100));\n    };\n    // 获取匹配项的函数\n    const getGlobalLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                entries.forEach(entry => {\n                    matches.push({ bookName: book.name, entry });\n                });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm.toLowerCase());\n                entries.forEach(entry => {\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName: book.name, entry });\n                    }\n                });\n            });\n        }\n        return matches;\n    };\n    const getCharacterLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter || linkedBooks.length === 0) {\n            return matches;\n        }\n        linkedBooks.forEach(bookName => {\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (!searchTerm) {\n                // 如果没有搜索词，返回所有条目\n                entries.forEach(entry => {\n                    matches.push({ bookName, entry });\n                });\n            }\n            else {\n                // 根据搜索词和过滤器获取匹配项\n                entries.forEach(entry => {\n                    const bookNameMatches = appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm.toLowerCase());\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName, entry });\n                    }\n                });\n            }\n        });\n        return matches;\n    };\n    const getChatLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat || !bookName) {\n            return matches;\n        }\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            entries.forEach(entry => {\n                matches.push({ bookName, entry });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            entries.forEach(entry => {\n                const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                const contentMatch = appState.searchFilters.content &&\n                    entry.content &&\n                    entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                if (entryNameMatches || keywordsMatch || contentMatch) {\n                    matches.push({ bookName, entry });\n                }\n            });\n        }\n        return matches;\n    };\n    // --- SortableJS 加载和拖拽排序功能 ---\n    const loadSortableJS = (callback) => {\n        if (parentWin.Sortable) {\n            callback();\n            return;\n        }\n        const script = parentWin.document.createElement('script');\n        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';\n        script.onload = () => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');\n            callback();\n        };\n        script.onerror = () => {\n            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');\n            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });\n        };\n        parentWin.document.head.appendChild(script);\n    };\n    // 防抖函数\n    const debounce = (func, delay) => {\n        let timeout;\n        return (...args) => {\n            clearTimeout(timeout);\n            timeout = setTimeout(() => func(...args), delay);\n        };\n    };\n    // 防抖保存正则顺序\n    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {\n        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];\n        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));\n        await TavernAPI.saveSettings();\n        showSuccessTick('正则顺序已保存');\n    }), 800);\n    // 处理正则拖拽结束事件\n    const handleRegexDragEnd = errorCatched(async (evt, scope) => {\n        const { oldIndex, newIndex } = evt;\n        if (oldIndex === newIndex)\n            return;\n        const targetList = appState.regexes[scope];\n        const [movedItem] = targetList.splice(oldIndex, 1);\n        targetList.splice(newIndex, 0, movedItem);\n        // 乐观更新UI：重新渲染序号\n        renderContent();\n        // 防抖保存\n        debouncedSaveRegexOrder();\n    });\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 加载 SortableJS 然后初始化 UI\n        loadSortableJS(() => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');\n            // 创建主面板\n            createMainPanel();\n            // 创建扩展菜单按钮\n            createExtensionButton();\n            // 绑定事件处理器\n            bindEventHandlers();\n            // 加载初始数据\n            loadAllData();\n            console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n        });\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <input type=\"text\" id=\"wio-replace-input\" placeholder=\"替换为...\" class=\"wio-search-input\">\r\n                            <button id=\"wio-replace-btn\" class=\"wio-btn wio-search-btn\" title=\"替换\">\r\n                                <i class=\"fa-solid fa-exchange-alt\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                        <div id=\"wio-multi-select-controls\" class=\"wio-multi-select-controls\" style=\"display: none;\">\r\n                            <div class=\"wio-multi-select-actions\">\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-all-btn\">全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-none-btn\">取消全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-invert-btn\">反选</button>\r\n                                <button class=\"wio-multi-select-action-btn enable\" id=\"wio-batch-enable-btn\">批量启用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-disable-btn\">批量禁用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-delete-btn\">批量删除</button>\r\n                                <span class=\"wio-selection-count\" id=\"wio-selection-count\">已选择: 0</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5 interactable\" title=\"${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n            console.log(`[WorldInfoOptimizer] Button #${BUTTON_ID} appended to #extensionsMenu.`);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                .wio-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 90%;\r\n                    max-width: 1200px;\r\n                    height: 80%;\r\n                    background: #2a2a2a;\r\n                    border: 1px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);\r\n                    z-index: 10000;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n                .wio-item-container {\r\n                    margin-bottom: 10px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                }\r\n                .wio-item-container.enabled {\r\n                    border-left: 3px solid #28a745;\r\n                }\r\n                .wio-item-container.selected {\r\n                    background: #2a4a6b;\r\n                    border-color: #007bff;\r\n                }\r\n                .wio-item-header {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    padding: 10px 15px;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-item-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-item-name {\r\n                    flex: 1;\r\n                    margin-left: 10px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-item-controls {\r\n                    display: flex;\r\n                    gap: 5px;\r\n                }\r\n                .wio-action-btn-icon {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn-icon:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-toggle-btn {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #dc3545;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-toggle-btn:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-book-group {\r\n                    margin-bottom: 20px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 6px;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-book-group-header, .wio-global-book-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px 6px 0 0;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-book-group-header:hover, .wio-global-book-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-book-name {\r\n                    font-size: 16px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status {\r\n                    margin-left: 10px;\r\n                    padding: 2px 8px;\r\n                    border-radius: 12px;\r\n                    font-size: 12px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-book-status.enabled {\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status.disabled {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-entry-actions, .wio-regex-actions {\r\n                    padding: 15px;\r\n                    border-bottom: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-action-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-multi-select-controls {\r\n                    margin-top: 10px;\r\n                    padding: 10px;\r\n                    background: #333;\r\n                    border-radius: 4px;\r\n                }\r\n                .wio-multi-select-actions {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                    align-items: center;\r\n                }\r\n                .wio-multi-select-action-btn {\r\n                    padding: 6px 12px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-multi-select-action-btn:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-multi-select-action-btn.enable {\r\n                    background: #28a745;\r\n                }\r\n                .wio-multi-select-action-btn.enable:hover {\r\n                    background: #218838;\r\n                }\r\n                .wio-multi-select-action-btn.disable {\r\n                    background: #dc3545;\r\n                }\r\n                .wio-multi-select-action-btn.disable:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-selection-count {\r\n                    margin-left: auto;\r\n                    color: #ccc;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-info-text-small {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 20px 0;\r\n                    font-size: 14px;\r\n                }\r\n                .wio-used-by-chars {\r\n                    margin-top: 5px;\r\n                    font-size: 12px;\r\n                    color: #aaa;\r\n                }\r\n                .wio-used-by-chars span {\r\n                    background: #555;\r\n                    padding: 2px 6px;\r\n                    border-radius: 3px;\r\n                    margin-right: 5px;\r\n                }\r\n                .wio-toast-notification {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-toast-notification.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-progress-toast {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-progress-toast.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-modal-overlay {\r\n                    position: fixed;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    background: rgba(0,0,0,0.7);\r\n                    z-index: 10002;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                }\r\n                .wio-modal-content {\r\n                    background: #2a2a2a;\r\n                    border-radius: 8px;\r\n                    max-width: 500px;\r\n                    width: 90%;\r\n                    max-height: 80vh;\r\n                    overflow-y: auto;\r\n                }\r\n                .wio-modal-header {\r\n                    padding: 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-body {\r\n                    padding: 20px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-modal-input {\r\n                    width: 100%;\r\n                    padding: 10px;\r\n                    margin-top: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-input.wio-input-error {\r\n                    border-color: #dc3545;\r\n                    animation: shake 0.5s;\r\n                }\r\n                @keyframes shake {\r\n                    0%, 100% { transform: translateX(0); }\r\n                    25% { transform: translateX(-5px); }\r\n                    75% { transform: translateX(5px); }\r\n                }\r\n                .wio-modal-footer {\r\n                    padding: 20px;\r\n                    border-top: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-modal-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-modal-ok {\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-ok:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-modal-cancel {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-cancel:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-drag-handle {\r\n                    cursor: grab;\r\n                    color: #ccc;\r\n                    margin-right: 10px;\r\n                    padding: 0 5px;\r\n                    opacity: 0.6;\r\n                    transition: opacity 0.2s;\r\n                }\r\n                .wio-drag-handle:hover {\r\n                    opacity: 1;\r\n                }\r\n                .wio-drag-handle:active {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-item-container.sortable-ghost {\r\n                    opacity: 0.4;\r\n                    background: #2a4a6b;\r\n                }\r\n                .wio-item-container.sortable-chosen {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-order-indicator {\r\n                    display: inline-block;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    font-size: 10px;\r\n                    font-weight: bold;\r\n                    padding: 2px 6px;\r\n                    border-radius: 10px;\r\n                    margin-right: 8px;\r\n                    min-width: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-regex-list {\r\n                    padding: 15px;\r\n                }\r\n                /* 按钮激活状态 */\r\n                #${BUTTON_ID}.active {\r\n                    background-color: rgba(126, 183, 213, 0.3) !important;\r\n                    border-color: #7eb7d5 !important;\r\n                }\r\n                #${BUTTON_ID}:hover {\r\n                    background-color: rgba(126, 183, 213, 0.15);\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 面板显示/隐藏函数 ---\n    const hidePanel = () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.hide();\n        $(`#${BUTTON_ID}`, parentDoc).removeClass('active');\n        $parentBody.off('mousedown.wio-outside-click');\n    };\n    const showPanel = async () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.css('display', 'flex');\n        $(`#${BUTTON_ID}`, parentDoc).addClass('active');\n        // 点击外部关闭面板\n        $parentBody.on('mousedown.wio-outside-click', function (event) {\n            if ($(event.target).closest(`#${PANEL_ID}`).length === 0 &&\n                $(event.target).closest(`#${BUTTON_ID}`).length === 0) {\n                hidePanel();\n            }\n        });\n        if (!appState.isDataLoaded) {\n            await loadAllData();\n        }\n        else {\n            renderContent();\n        }\n    };\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                hidePanel();\n            }\n            else {\n                await showPanel();\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            hidePanel();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {\n            const $this = $(event.currentTarget);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 替换按钮\n        $(parentDoc).on('click', '#wio-replace-btn', () => {\n            handleReplace();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            const $this = $(event.currentTarget);\n            if (appState.multiSelectMode) {\n                $this.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n            }\n            else {\n                $this.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    $panel.hide();\n                }\n            }\n        });\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();