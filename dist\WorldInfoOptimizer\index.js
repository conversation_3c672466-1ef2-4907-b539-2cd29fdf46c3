var __webpack_modules__ = {
  "./src/WorldInfoOptimizer/index.ts": 
  /*!*****************************************!*\
  !*** ./src/WorldInfoOptimizer/index.ts ***!
  \*****************************************/ () => {
    eval("{// ==UserScript==\n// @name         世界书优化器 (World Info Optimizer)\n// @namespace    SillyTavern.WorldInfoOptimizer.v1_0_0\n// @match        */*\n// @version      1.0.0\n// @description  【世界书管理专家】基于 Linus 哲学的简洁高效世界书管理工具。提供搜索、替换、批量操作等核心功能。\n// <AUTHOR> & AI Assistant\n// @grant        none\n// @inject-into  content\n// ==/UserScript==\n\n// 使用IIFE封装，避免全局污染\n(() => {\n    console.log('[WorldInfoOptimizer] Script execution started.');\n    // --- 配置常量 ---\n    const SCRIPT_VERSION_TAG = 'v1_0_0';\n    const PANEL_ID = 'world-info-optimizer-panel';\n    const BUTTON_ID = 'world-info-optimizer-button';\n    const BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\n    const BUTTON_TOOLTIP = '世界书优化器';\n    const BUTTON_TEXT_IN_MENU = '世界书优化器';\n    const SEARCH_INPUT_ID = 'wio-search-input';\n    const REFRESH_BTN_ID = 'wio-refresh-btn';\n    const COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\n    const COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\n    const CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n    const LOREBOOK_OPTIONS = {\n        position: {\n            before_character_definition: '角色定义前',\n            after_character_definition: '角色定义后',\n            before_example_messages: '聊天示例前',\n            after_example_messages: '聊天示例后',\n            before_author_note: '作者笔记前',\n            after_author_note: '作者笔记后',\n            at_depth_as_system: '@D ⚙ 系统',\n            at_depth_as_assistant: '@D 🗨️ 角色',\n            at_depth_as_user: '@D 👤 用户',\n        },\n        logic: {\n            and_any: '任一 AND',\n            and_all: '所有 AND',\n            not_any: '任一 NOT',\n            not_all: '所有 NOT',\n        },\n    };\n    // --- 应用程序状态 ---\n    const appState = {\n        regexes: { global: [], character: [] },\n        lorebooks: { character: [] },\n        chatLorebook: null,\n        allLorebooks: [],\n        lorebookEntries: new Map(),\n        lorebookUsage: new Map(),\n        activeTab: 'global-lore',\n        isDataLoaded: false,\n        searchFilters: { bookName: true, entryName: true, keywords: true, content: true },\n        multiSelectMode: false,\n        selectedItems: new Set(),\n    };\n    // --- 全局变量 ---\n    let parentWin;\n    let $;\n    let TavernHelper;\n    /**\n     * 等待DOM和API就绪\n     */\n    function onReady(callback) {\n        const domSelector = '#extensionsMenu';\n        const maxRetries = 100;\n        let retries = 0;\n        console.log(`[WorldInfoOptimizer] Starting readiness check. Polling for DOM element \"${domSelector}\" AND core APIs.`);\n        const interval = setInterval(() => {\n            const parentDoc = window.parent.document;\n            parentWin = window.parent;\n            const domReady = parentDoc.querySelector(domSelector) !== null;\n            const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n            if (domReady && apiReady) {\n                clearInterval(interval);\n                console.log(`[WorldInfoOptimizer] SUCCESS: Both DOM and Core APIs are ready. Initializing script.`);\n                try {\n                    callback(parentWin.jQuery, parentWin.TavernHelper);\n                }\n                catch (e) {\n                    console.error('[WorldInfoOptimizer] FATAL: Error during main callback execution.', e);\n                }\n            }\n            else {\n                retries++;\n                if (retries > maxRetries) {\n                    clearInterval(interval);\n                    console.error(`[WorldInfoOptimizer] FATAL: Readiness check timed out.`);\n                    if (!domReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: DOM element \"${domSelector}\" not found.`);\n                    if (!apiReady)\n                        console.error(`[WorldInfoOptimizer] -> Failure: Core APIs not available.`);\n                }\n            }\n        }, 150);\n    }\n    /**\n     * 错误处理包装器\n     */\n    const errorCatched = (fn, context = 'WorldInfoOptimizer') => async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                await showModal({\n                    type: 'alert',\n                    title: '脚本异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n        }\n    };\n    // --- 安全访问 lorebookEntries 的函数 ---\n    const safeGetLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.get !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.get is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            const entries = appState.lorebookEntries.get(bookName);\n            return Array.isArray(entries) ? entries : [];\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeGetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return [];\n        }\n    };\n    const safeSetLorebookEntries = (bookName, entries) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            if (typeof appState.lorebookEntries.set !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.set is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n            }\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeSetLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            appState.lorebookEntries.set(bookName, Array.isArray(entries) ? entries : []);\n        }\n    };\n    const safeDeleteLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.delete !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.delete is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.delete(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeDeleteLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeClearLorebookEntries = () => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            if (typeof appState.lorebookEntries.clear !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.clear is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return;\n            }\n            appState.lorebookEntries.clear();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeClearLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n        }\n    };\n    const safeHasLorebookEntries = (bookName) => {\n        try {\n            if (!appState.lorebookEntries || !(appState.lorebookEntries instanceof Map)) {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries is not a Map, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            if (typeof appState.lorebookEntries.has !== 'function') {\n                console.warn('[WorldInfoOptimizer] appState.lorebookEntries.has is not a function, reinitializing...');\n                appState.lorebookEntries = new Map();\n                return false;\n            }\n            return appState.lorebookEntries.has(bookName);\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in safeHasLorebookEntries:', error);\n            appState.lorebookEntries = new Map();\n            return false;\n        }\n    };\n    // --- 工具函数 ---\n    const escapeHtml = (text) => {\n        if (typeof text !== 'string')\n            return String(text);\n        const div = document.createElement('div');\n        div.textContent = text;\n        return div.innerHTML;\n    };\n    const highlightText = (text, searchTerm) => {\n        if (!searchTerm || !text)\n            return escapeHtml(text);\n        const escapedText = escapeHtml(text);\n        const htmlSafeSearchTerm = escapeHtml(searchTerm);\n        const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n        return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n    };\n    // --- 通知和模态框函数 ---\n    const showSuccessTick = (message = '操作成功', duration = 1500) => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return;\n        $panel.find('.wio-toast-notification').remove();\n        const toastHtml = `<div class=\"wio-toast-notification\"><i class=\"fa-solid fa-check-circle\"></i> ${escapeHtml(message)}</div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        setTimeout(() => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        }, duration);\n    };\n    const showProgressToast = (initialMessage = '正在处理...') => {\n        const $panel = $(`#${PANEL_ID}`, parentWin.document);\n        if ($panel.length === 0)\n            return { update: () => { }, remove: () => { } };\n        $panel.find('.wio-progress-toast').remove();\n        const toastHtml = `<div class=\"wio-progress-toast\"><i class=\"fa-solid fa-spinner fa-spin\"></i> <span class=\"wio-progress-text\">${escapeHtml(initialMessage)}</span></div>`;\n        const $toast = $(toastHtml);\n        $panel.append($toast);\n        setTimeout(() => {\n            $toast.addClass('visible');\n        }, 10);\n        const update = (newMessage) => {\n            $toast.find('.wio-progress-text').html(escapeHtml(newMessage));\n        };\n        const remove = () => {\n            $toast.removeClass('visible');\n            setTimeout(() => {\n                $toast.remove();\n            }, 300);\n        };\n        return { update, remove };\n    };\n    const showModal = (options) => {\n        return new Promise((resolve, reject) => {\n            const { type = 'alert', title = '通知', text = '', placeholder = '', value = '' } = options;\n            let buttonsHtml = '';\n            if (type === 'alert')\n                buttonsHtml = '<button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            else if (type === 'confirm')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确认</button>';\n            else if (type === 'prompt')\n                buttonsHtml =\n                    '<button class=\"wio-modal-btn wio-modal-cancel\">取消</button><button class=\"wio-modal-btn wio-modal-ok\">确定</button>';\n            const inputHtml = type === 'prompt'\n                ? `<input type=\"text\" class=\"wio-modal-input\" placeholder=\"${escapeHtml(placeholder)}\" value=\"${escapeHtml(value)}\">`\n                : '';\n            const modalHtml = `<div class=\"wio-modal-overlay\"><div class=\"wio-modal-content\"><div class=\"wio-modal-header\">${escapeHtml(title)}</div><div class=\"wio-modal-body\"><p>${escapeHtml(text)}</p>${inputHtml}</div><div class=\"wio-modal-footer\">${buttonsHtml}</div></div></div>`;\n            const $modal = $(modalHtml).hide();\n            const $panel = $(`#${PANEL_ID}`, parentWin.document);\n            if ($panel.length > 0) {\n                $panel.append($modal);\n            }\n            else {\n                $('body', parentWin.document).append($modal);\n            }\n            $modal.fadeIn(200);\n            const $input = $modal.find('.wio-modal-input');\n            if (type === 'prompt')\n                $input.focus().select();\n            const closeModal = (isSuccess, val) => {\n                $modal.fadeOut(200, () => {\n                    $modal.remove();\n                    if (isSuccess)\n                        resolve(val);\n                    else\n                        reject();\n                });\n            };\n            $modal.on('click', '.wio-modal-ok', () => {\n                const val = type === 'prompt' ? $input.val() : true;\n                if (type === 'prompt' && !String(val).trim()) {\n                    $input.addClass('wio-input-error');\n                    setTimeout(() => $input.removeClass('wio-input-error'), 500);\n                    return;\n                }\n                closeModal(true, val);\n            });\n            $modal.on('click', '.wio-modal-cancel', () => closeModal(false));\n            if (type === 'prompt') {\n                $input.on('keydown', (e) => {\n                    if (e.key === 'Enter')\n                        $modal.find('.wio-modal-ok').click();\n                    else if (e.key === 'Escape')\n                        closeModal(false);\n                });\n            }\n        });\n    };\n    // --- API 包装器 ---\n    let TavernAPI = null;\n    const initializeTavernAPI = () => {\n        TavernAPI = {\n            createLorebook: errorCatched(async (name) => await TavernHelper.createLorebook(name)),\n            deleteLorebook: errorCatched(async (name) => await TavernHelper.deleteLorebook(name)),\n            getLorebooks: errorCatched(async () => await TavernHelper.getLorebooks()),\n            setLorebookSettings: errorCatched(async (settings) => await TavernHelper.setLorebookSettings(settings)),\n            getCharData: errorCatched(async () => await TavernHelper.getCharData()),\n            Character: TavernHelper.Character || null,\n            getRegexes: errorCatched(async () => await TavernHelper.getTavernRegexes({ scope: 'all' })),\n            replaceRegexes: errorCatched(async (regexes) => await TavernHelper.replaceTavernRegexes(regexes, { scope: 'all' })),\n            getLorebookSettings: errorCatched(async () => await TavernHelper.getLorebookSettings()),\n            getCharLorebooks: errorCatched(async (charData) => await TavernHelper.getCharLorebooks(charData)),\n            getCurrentCharLorebooks: errorCatched(async () => await TavernHelper.getCharLorebooks()),\n            getChatLorebook: errorCatched(async () => await TavernHelper.getChatLorebook()),\n            getOrCreateChatLorebook: errorCatched(async (name) => await TavernHelper.getOrCreateChatLorebook(name)),\n            setChatLorebook: errorCatched(async (name) => await TavernHelper.setChatLorebook(name)),\n            getLorebookEntries: errorCatched(async (name) => await TavernHelper.getLorebookEntries(name)),\n            setLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.setLorebookEntries(name, entries)),\n            createLorebookEntries: errorCatched(async (name, entries) => await TavernHelper.createLorebookEntries(name, entries)),\n            deleteLorebookEntries: errorCatched(async (name, uids) => await TavernHelper.deleteLorebookEntries(name, uids)),\n            saveSettings: errorCatched(async () => await TavernHelper.builtin.saveSettings()),\n            setCurrentCharLorebooks: errorCatched(async (lorebooks) => await TavernHelper.setCurrentCharLorebooks(lorebooks)),\n        };\n    };\n    // --- 数据加载函数 ---\n    const loadAllData = errorCatched(async () => {\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.html('<p class=\"wio-info-text\">正在加载所有数据，请稍候...</p>');\n        try {\n            // 防御性检查：确保SillyTavern API可用\n            if (!parentWin.SillyTavern || !parentWin.SillyTavern.getContext) {\n                console.warn('[WorldInfoOptimizer] SillyTavern API not available, initializing with empty data');\n                appState.regexes.global = [];\n                appState.regexes.character = [];\n                appState.allLorebooks = [];\n                appState.lorebooks.character = [];\n                appState.chatLorebook = null;\n                safeClearLorebookEntries();\n                appState.isDataLoaded = true;\n                renderContent();\n                return;\n            }\n            const context = parentWin.SillyTavern.getContext() || {};\n            const allCharacters = Array.isArray(context.characters) ? context.characters : [];\n            const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n            const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n            let charData = null, charLinkedBooks = null, chatLorebook = null;\n            // 使用Promise.allSettled来避免单个失败影响整体\n            const promises = [\n                TavernAPI.getRegexes().catch(() => []),\n                TavernAPI.getLorebookSettings().catch(() => ({})),\n                TavernAPI.getLorebooks().catch(() => []),\n            ];\n            if (hasActiveCharacter) {\n                promises.push(TavernAPI.getCharData().catch(() => null));\n                promises.push(TavernAPI.getCurrentCharLorebooks().catch(() => null));\n            }\n            else {\n                promises.push(Promise.resolve(null), Promise.resolve(null));\n            }\n            if (hasActiveChat) {\n                promises.push(TavernAPI.getChatLorebook().catch((error) => {\n                    console.warn('[WorldInfoOptimizer] Failed to get chat lorebook:', error);\n                    return null;\n                }));\n            }\n            else {\n                promises.push(Promise.resolve(null));\n            }\n            const results = await Promise.allSettled(promises);\n            // 安全提取结果\n            const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n            const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n            const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n            charData = results[3]?.status === 'fulfilled' ? results[3].value : null;\n            charLinkedBooks = results[4]?.status === 'fulfilled' ? results[4].value : null;\n            chatLorebook = results[5]?.status === 'fulfilled' ? results[5].value : null;\n            appState.regexes.global = Array.isArray(allUIRegexes)\n                ? allUIRegexes.filter((r) => r.scope === 'global')\n                : [];\n            updateCharacterRegexes(allUIRegexes, charData);\n            safeClearLorebookEntries();\n            appState.lorebookUsage.clear();\n            const knownBookNames = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 安全处理角色世界书\n            if (Array.isArray(allCharacters) && allCharacters.length > 0) {\n                try {\n                    await Promise.all(allCharacters.map(async (char) => {\n                        if (!char || !char.name)\n                            return;\n                        try {\n                            let books = null;\n                            try {\n                                const result = TavernHelper.getCharLorebooks({ name: char.name });\n                                if (result && typeof result.then === 'function') {\n                                    books = await result;\n                                }\n                                else {\n                                    books = result;\n                                }\n                            }\n                            catch (error) {\n                                console.warn(`[WorldInfoOptimizer] Error getting lorebooks for character \"${char.name}\":`, error);\n                                books = null;\n                            }\n                            if (books && typeof books === 'object') {\n                                const bookSet = new Set();\n                                if (books.primary && typeof books.primary === 'string')\n                                    bookSet.add(books.primary);\n                                if (Array.isArray(books.additional)) {\n                                    books.additional.forEach((b) => typeof b === 'string' && bookSet.add(b));\n                                }\n                                bookSet.forEach(bookName => {\n                                    if (typeof bookName === 'string') {\n                                        if (!appState.lorebookUsage.has(bookName)) {\n                                            appState.lorebookUsage.set(bookName, []);\n                                        }\n                                        appState.lorebookUsage.get(bookName).push(char.name);\n                                        knownBookNames.add(bookName);\n                                        console.log(`[WorldInfoOptimizer] Character \"${char.name}\" uses lorebook \"${bookName}\"`);\n                                    }\n                                });\n                            }\n                        }\n                        catch (charError) {\n                            console.warn(`[WorldInfoOptimizer] Error processing character ${char.name}:`, charError);\n                        }\n                    }));\n                }\n                catch (charProcessingError) {\n                    console.warn('[WorldInfoOptimizer] Error processing characters:', charProcessingError);\n                }\n            }\n            const enabledGlobalBooks = new Set(Array.isArray(globalSettings?.selected_global_lorebooks) ? globalSettings.selected_global_lorebooks : []);\n            appState.allLorebooks = (Array.isArray(allBookFileNames) ? allBookFileNames : []).map((name) => ({\n                name: name,\n                enabled: enabledGlobalBooks.has(name),\n            }));\n            const charBookSet = new Set();\n            if (charLinkedBooks && typeof charLinkedBooks === 'object') {\n                if (charLinkedBooks.primary && typeof charLinkedBooks.primary === 'string') {\n                    charBookSet.add(charLinkedBooks.primary);\n                }\n                if (Array.isArray(charLinkedBooks.additional)) {\n                    charLinkedBooks.additional.forEach((name) => typeof name === 'string' && charBookSet.add(name));\n                }\n            }\n            appState.lorebooks.character = Array.from(charBookSet);\n            appState.chatLorebook = typeof chatLorebook === 'string' ? chatLorebook : null;\n            if (typeof chatLorebook === 'string') {\n                knownBookNames.add(chatLorebook);\n            }\n            const allBooksToLoad = Array.from(knownBookNames);\n            const existingBookFiles = new Set(Array.isArray(allBookFileNames) ? allBookFileNames : []);\n            // 分批加载世界书条目，避免同时加载过多\n            const batchSize = 5;\n            for (let i = 0; i < allBooksToLoad.length; i += batchSize) {\n                const batch = allBooksToLoad.slice(i, i + batchSize);\n                await Promise.allSettled(batch.map(async (name) => {\n                    if (existingBookFiles.has(name) && typeof name === 'string') {\n                        try {\n                            const entries = await TavernAPI.getLorebookEntries(name).catch(() => []);\n                            safeSetLorebookEntries(name, entries);\n                        }\n                        catch (entryError) {\n                            console.warn(`[WorldInfoOptimizer] Error loading entries for book ${name}:`, entryError);\n                        }\n                    }\n                }));\n            }\n            appState.isDataLoaded = true;\n            renderContent();\n        }\n        catch (error) {\n            console.error('[WorldInfoOptimizer] Error in loadAllData:', error);\n            $content.html(`\r\n                <div style=\"padding: 20px; text-align: center;\">\r\n                    <p style=\"color: #ff6b6b; margin-bottom: 10px;\">\r\n                        <i class=\"fa-solid fa-exclamation-triangle\"></i> 数据加载失败\r\n                    </p>\r\n                    <p style=\"color: #666; font-size: 14px;\">\r\n                        请检查开发者控制台获取详细信息，或尝试刷新页面。\r\n                    </p>\r\n                    <button class=\"wio-modal-btn\" onclick=\"$('#${REFRESH_BTN_ID}').click()\"\r\n                            style=\"margin-top: 15px; padding: 8px 16px;\">\r\n                        <i class=\"fa-solid fa-refresh\"></i> 重试\r\n                    </button>\r\n                </div>\r\n            `);\n            throw error;\n        }\n    });\n    // --- 角色正则和世界书更新函数 ---\n    function updateCharacterRegexes(allUIRegexes, charData) {\n        const characterUIRegexes = allUIRegexes?.filter((r) => r.scope === 'character') || [];\n        let cardRegexes = [];\n        if (charData && TavernAPI && TavernAPI.Character) {\n            try {\n                const character = new TavernAPI.Character(charData);\n                cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                    id: r.id || `card-${Date.now()}-${i}`,\n                    script_name: r.scriptName || '未命名卡内正则',\n                    find_regex: r.findRegex,\n                    replace_string: r.replaceString,\n                    enabled: !r.disabled,\n                    scope: 'character',\n                    source: 'card',\n                }));\n            }\n            catch (e) {\n                console.warn('无法解析角色卡正则脚本:', e);\n            }\n        }\n        const uiRegexIdentifiers = new Set(characterUIRegexes.map((r) => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n        const uniqueCardRegexes = cardRegexes.filter((r) => {\n            const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n            return !uiRegexIdentifiers.has(identifier);\n        });\n        appState.regexes.character = [...characterUIRegexes, ...uniqueCardRegexes];\n    }\n    function updateCharacterLorebooks(charBooks) {\n        const characterBookNames = [];\n        if (charBooks) {\n            if (charBooks.primary)\n                characterBookNames.push(charBooks.primary);\n            if (charBooks.additional)\n                characterBookNames.push(...charBooks.additional);\n        }\n        appState.lorebooks.character = [...new Set(characterBookNames)];\n    }\n    // --- 渲染函数 ---\n    const renderContent = () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val()?.toLowerCase() || '';\n        appState.searchFilters.bookName = $(`#wio-filter-book-name`, parentWin.document).is(':checked');\n        appState.searchFilters.entryName = $(`#wio-filter-entry-name`, parentWin.document).is(':checked');\n        appState.searchFilters.keywords = $(`#wio-filter-keywords`, parentWin.document).is(':checked');\n        appState.searchFilters.content = $(`#wio-filter-content`, parentWin.document).is(':checked');\n        const $content = $(`#${PANEL_ID}-content`, parentWin.document);\n        $content.empty();\n        $(`#${PANEL_ID}`, parentWin.document).toggleClass('wio-multi-select-mode', appState.multiSelectMode);\n        const isLoreTab = appState.activeTab === 'global-lore' || appState.activeTab === 'char-lore' || appState.activeTab === 'chat-lore';\n        $(`#wio-search-filters-container`, parentWin.document).toggle(isLoreTab);\n        $(`#wio-multi-select-controls`, parentWin.document).toggle(appState.multiSelectMode);\n        updateSelectionCount();\n        switch (appState.activeTab) {\n            case 'global-lore':\n                renderGlobalLorebookView(searchTerm, $content);\n                break;\n            case 'char-lore':\n                renderCharacterLorebookView(searchTerm, $content);\n                break;\n            case 'chat-lore':\n                renderChatLorebookView(searchTerm, $content);\n                break;\n            case 'global-regex':\n                renderRegexView(appState.regexes.global, searchTerm, $content, '全局正则');\n                break;\n            case 'char-regex':\n                renderRegexView(appState.regexes.character, searchTerm, $content, '角色正则');\n                break;\n        }\n    };\n    // --- 选择和批量操作函数 ---\n    const updateSelectionCount = () => {\n        $(`#wio-selection-count`, parentWin.document).text(`已选择: ${appState.selectedItems.size}`);\n    };\n    const getAllVisibleItems = () => {\n        const visibleItems = [];\n        const activeTab = appState.activeTab;\n        if (activeTab === 'global-lore') {\n            appState.allLorebooks.forEach(book => {\n                visibleItems.push({ type: 'book', id: book.name, enabled: book.enabled });\n                [...safeGetLorebookEntries(book.name)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName: book.name, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'char-lore') {\n            appState.lorebooks.character.forEach(bookName => {\n                [...safeGetLorebookEntries(bookName)].forEach(entry => {\n                    visibleItems.push({ type: 'lore', id: entry.uid, bookName, enabled: entry.enabled });\n                });\n            });\n        }\n        else if (activeTab === 'global-regex') {\n            appState.regexes.global.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        else if (activeTab === 'char-regex') {\n            appState.regexes.character.forEach(regex => {\n                visibleItems.push({ type: 'regex', id: regex.id, enabled: regex.enabled });\n            });\n        }\n        return visibleItems;\n    };\n    const renderGlobalLorebookView = (searchTerm, $container) => {\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        let filteredBookData = [];\n        if (!searchTerm) {\n            filteredBookData = books.map(book => ({ book, forceShowAllEntries: true, filteredEntries: null }));\n        }\n        else {\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm);\n                const matchingEntries = entries.filter(entry => (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                    (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n                if (bookNameMatches || matchingEntries.length > 0) {\n                    filteredBookData.push({ book, forceShowAllEntries: bookNameMatches, filteredEntries: matchingEntries });\n                }\n            });\n        }\n        if (filteredBookData.length === 0 && appState.allLorebooks.length > 0) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书。</p>`);\n        }\n        else if (appState.allLorebooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">还没有世界书，点击上方\"+\"创建一个吧。</p>`);\n        }\n        filteredBookData.forEach(data => {\n            if (data && data.book) {\n                $container.append(createGlobalLorebookElement(data.book, searchTerm, data.forceShowAllEntries, data.filteredEntries));\n            }\n        });\n    };\n    const renderCharacterLorebookView = (searchTerm, $container) => {\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter) {\n            $container.html(`<p class=\"wio-info-text\">请先加载一个角色以管理角色世界书。</p>`);\n            return;\n        }\n        if (linkedBooks.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">当前角色没有绑定的世界书。点击同步按钮刷新。</p>`);\n            return;\n        }\n        const renderBook = (bookName) => {\n            const $bookContainer = $(`\r\n        <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n          <div class=\"wio-book-group-header\">\r\n            <span>${escapeHtml(bookName)}</span>\r\n            <div class=\"wio-item-controls\">\r\n              <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n              <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"wio-entry-list-wrapper\"></div>\r\n        </div>\r\n      `);\n            const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n            const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button><button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-shield-halved\"></i> 全开防递</button><button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-check-double\"></i> 修复关键词</button></div>`);\n            $listWrapper.append($entryActions);\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            const bookNameMatches = !searchTerm || (appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm));\n            const matchingEntries = entries.filter(entry => !searchTerm ||\n                (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n                (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n            if (!bookNameMatches && matchingEntries.length === 0)\n                return null;\n            const entriesToShow = bookNameMatches ? entries : matchingEntries;\n            if (entriesToShow.length === 0 && searchTerm) {\n                $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n            }\n            else {\n                entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n            }\n            return $bookContainer;\n        };\n        let renderedCount = 0;\n        linkedBooks.forEach(bookName => {\n            const $el = renderBook(bookName);\n            if ($el) {\n                $container.append($el);\n                renderedCount++;\n            }\n        });\n        if (renderedCount === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的世界书或条目。</p>`);\n        }\n    };\n    const renderChatLorebookView = (searchTerm, $container) => {\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat) {\n            $container.html(`<p class=\"wio-info-text\">请先开始一个聊天以管理聊天世界书。</p>`);\n            return;\n        }\n        if (!bookName) {\n            $container.html(`\r\n        <div class=\"wio-info-section\">\r\n          <p class=\"wio-info-text\">当前聊天没有绑定世界书。</p>\r\n          <button id=\"wio-create-chat-lore-btn\" class=\"wio-btn wio-btn-primary\">\r\n            <i class=\"fa-solid fa-plus\"></i> 创建聊天世界书\r\n          </button>\r\n        </div>\r\n      `);\n            return;\n        }\n        const $bookContainer = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(bookName)}\">\r\n        <div class=\"wio-book-group-header\">\r\n          <span>${escapeHtml(bookName)} (聊天世界书)</span>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-unlink-chat-lore-btn\" title=\"解除绑定\"><i class=\"fa-solid fa-unlink\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-entry-list-wrapper\"></div>\r\n      </div>\r\n    `);\n        const $listWrapper = $bookContainer.find('.wio-entry-list-wrapper');\n        const $entryActions = $(`<div class=\"wio-entry-actions\"><button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(bookName)}\"><i class=\"fa-solid fa-plus\"></i> 新建条目</button></div>`);\n        $listWrapper.append($entryActions);\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const matchingEntries = entries.filter(entry => !searchTerm ||\n            (appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm)) ||\n            (appState.searchFilters.content && entry.content && entry.content.toLowerCase().includes(searchTerm)));\n        if (matchingEntries.length === 0 && searchTerm) {\n            $listWrapper.append(`<p class=\"wio-info-text-small\">无匹配条目</p>`);\n        }\n        else {\n            matchingEntries.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', bookName, searchTerm)));\n        }\n        $container.empty().append($bookContainer);\n    };\n    const renderRegexView = (regexes, searchTerm, $container, title) => {\n        if (regexes.length === 0) {\n            $container.html(`<p class=\"wio-info-text\">没有找到${title}。</p>`);\n            return;\n        }\n        // 按启用状态和名称排序\n        const sortedRegexes = [...regexes].sort((a, b) => Number(b.enabled) - Number(a.enabled) || (a.script_name || '').localeCompare(b.script_name || ''));\n        // 过滤匹配项\n        let filteredRegexes = sortedRegexes;\n        if (searchTerm) {\n            filteredRegexes = sortedRegexes.filter(regex => {\n                const name = regex.script_name || '';\n                const findRegex = regex.find_regex || '';\n                const replaceString = regex.replace_string || '';\n                return (name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    findRegex.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                    replaceString.toLowerCase().includes(searchTerm.toLowerCase()));\n            });\n        }\n        if (filteredRegexes.length === 0 && searchTerm) {\n            $container.html(`<p class=\"wio-info-text\">未找到匹配的${title}。</p>`);\n            return;\n        }\n        // 添加操作按钮区域\n        const $actions = $(`\r\n      <div class=\"wio-regex-actions\">\r\n        <button class=\"wio-action-btn wio-create-regex-btn\" data-scope=\"${title === '全局正则' ? 'global' : 'character'}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-import-regex-btn\">\r\n          <i class=\"fa-solid fa-upload\"></i> 导入正则\r\n        </button>\r\n        <button class=\"wio-action-btn wio-export-regex-btn\">\r\n          <i class=\"fa-solid fa-download\"></i> 导出正则\r\n        </button>\r\n      </div>\r\n    `);\n        $container.append($actions);\n        // 渲染正则列表\n        const $regexList = $('<div class=\"wio-regex-list\"></div>');\n        filteredRegexes.forEach((regex, index) => {\n            const $element = createItemElement(regex, 'regex', '', searchTerm);\n            // 添加序号指示器\n            $element.find('.wio-item-name').prepend(`<span class=\"wio-order-indicator\">#${index + 1}</span> `);\n            $regexList.append($element);\n        });\n        $container.append($regexList);\n        // 初始化拖拽排序（仅对非搜索状态的完整列表）\n        if (!searchTerm && parentWin.Sortable) {\n            const listEl = $regexList[0];\n            if (listEl) {\n                new parentWin.Sortable(listEl, {\n                    animation: 150,\n                    handle: '.wio-drag-handle',\n                    ghostClass: 'sortable-ghost',\n                    chosenClass: 'sortable-chosen',\n                    onEnd: (evt) => handleRegexDragEnd(evt, title === '全局正则' ? 'global' : 'character'),\n                });\n            }\n        }\n    };\n    // --- 核心UI元素创建函数 ---\n    const createItemElement = (item, type, bookName = '', searchTerm = '') => {\n        const isLore = type === 'lore';\n        const id = isLore ? item.uid : item.id;\n        const name = isLore ? item.comment || '无标题条目' : item.script_name || '未命名正则';\n        const fromCard = item.source === 'card';\n        let controlsHtml = '';\n        if (isLore) {\n            // 所有世界书条目都有完整的操作按钮\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n        <button class=\"wio-action-btn-icon wio-delete-entry-btn\" title=\"删除条目\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n      `;\n        }\n        else if (fromCard) {\n            // 来自卡片的正则只有开关\n            controlsHtml =\n                '<button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>';\n        }\n        else {\n            // UI中的正则有重命名和开关\n            controlsHtml = `\r\n        <button class=\"wio-action-btn-icon wio-rename-btn\" title=\"重命名\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n        <button class=\"wio-toggle-btn wio-item-toggle\" title=\"启用/禁用此条目\"><i class=\"fa-solid fa-power-off\"></i></button>\r\n      `;\n        }\n        const dragHandleHtml = !fromCard && !isLore\n            ? '<span class=\"wio-drag-handle\" title=\"拖拽排序\"><i class=\"fa-solid fa-grip-vertical\"></i></span>'\n            : '';\n        // 应用高亮到条目名称\n        const highlightedName = highlightText(name, searchTerm);\n        const $element = $(`<div class=\"wio-item-container ${fromCard ? 'from-card' : ''}\" data-type=\"${type}\" data-id=\"${id}\" ${isLore ? `data-book-name=\"${escapeHtml(bookName)}\"` : ''}><div class=\"wio-item-header\" title=\"${fromCard ? '此条目来自角色卡，部分操作受限' : appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/编辑'}\">${dragHandleHtml}<span class=\"wio-item-name\">${highlightedName}</span><div class=\"wio-item-controls\">${controlsHtml}</div></div><div class=\"wio-collapsible-content\"></div></div>`);\n        // 保存搜索词以便在内容展开时使用\n        $element.data('searchTerm', searchTerm);\n        $element.toggleClass('enabled', item.enabled);\n        if (appState.multiSelectMode) {\n            const itemKey = isLore ? `lore:${bookName}:${id}` : `regex:${id}`;\n            $element.toggleClass('selected', appState.selectedItems.has(itemKey));\n        }\n        return $element;\n    };\n    const createGlobalLorebookElement = (book, searchTerm, forceShowAllEntries, filteredEntries) => {\n        const usedByChars = appState.lorebookUsage.get(book.name) || [];\n        const usedByHtml = usedByChars.length > 0\n            ? `<div class=\"wio-used-by-chars\">使用者: ${usedByChars.map(char => `<span>${escapeHtml(char)}</span>`).join(', ')}</div>`\n            : '';\n        const $element = $(`\r\n      <div class=\"wio-book-group\" data-book-name=\"${escapeHtml(book.name)}\">\r\n        <div class=\"wio-global-book-header\" title=\"${appState.multiSelectMode ? '点击选择/取消选择' : '点击展开/折叠'}\">\r\n          <div class=\"wio-book-info\">\r\n            <span class=\"wio-book-name\">${highlightText(book.name, searchTerm)}</span>\r\n            <span class=\"wio-book-status ${book.enabled ? 'enabled' : 'disabled'}\">${book.enabled ? '已启用' : '已禁用'}</span>\r\n            ${usedByHtml}\r\n          </div>\r\n          <div class=\"wio-item-controls\">\r\n            <button class=\"wio-action-btn-icon wio-rename-book-btn\" title=\"重命名世界书\"><i class=\"fa-solid fa-pencil\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-edit-entries-btn\" title=\"编辑条目\"><i class=\"fa-solid fa-edit\"></i></button>\r\n            <button class=\"wio-action-btn-icon wio-delete-book-btn\" title=\"删除世界书\"><i class=\"fa-solid fa-trash-can\"></i></button>\r\n          </div>\r\n        </div>\r\n        <div class=\"wio-collapsible-content\"></div>\r\n      </div>\r\n    `);\n        const $content = $element.find('.wio-collapsible-content');\n        // 添加条目操作按钮\n        const $entryActions = $(`\r\n      <div class=\"wio-entry-actions\">\r\n        <button class=\"wio-action-btn wio-create-entry-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-plus\"></i> 新建条目\r\n        </button>\r\n        <button class=\"wio-action-btn wio-batch-recursion-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-shield-halved\"></i> 全开防递\r\n        </button>\r\n        <button class=\"wio-action-btn wio-fix-keywords-btn\" data-book-name=\"${escapeHtml(book.name)}\">\r\n          <i class=\"fa-solid fa-check-double\"></i> 修复关键词\r\n        </button>\r\n      </div>\r\n    `);\n        $content.append($entryActions);\n        const allEntries = [...safeGetLorebookEntries(book.name)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        const entriesToShow = forceShowAllEntries ? allEntries : filteredEntries || [];\n        if (entriesToShow && entriesToShow.length > 0) {\n            const $listWrapper = $('<div class=\"wio-entry-list-wrapper\"></div>');\n            entriesToShow.forEach(entry => $listWrapper.append(createItemElement(entry, 'lore', book.name, searchTerm)));\n            $content.append($listWrapper);\n        }\n        else if (searchTerm) {\n            $content.append(`<div class=\"wio-info-text-small\">无匹配项</div>`);\n        }\n        return $element;\n    };\n    // --- 替换功能实现 ---\n    const handleReplace = errorCatched(async () => {\n        const searchTerm = $(`#${SEARCH_INPUT_ID}`, parentWin.document).val();\n        const replaceTerm = $('#wio-replace-input', parentWin.document).val();\n        // 检查搜索词是否为空\n        if (!searchTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入搜索词。' });\n            return;\n        }\n        // 检查替换词是否为空\n        if (!replaceTerm) {\n            await showModal({ type: 'alert', title: '替换失败', text: '请先输入替换词。' });\n            return;\n        }\n        // 获取当前视图的匹配项\n        let matches = [];\n        switch (appState.activeTab) {\n            case 'global-lore':\n                matches = getGlobalLorebookMatches(searchTerm);\n                break;\n            case 'char-lore':\n                matches = getCharacterLorebookMatches(searchTerm);\n                break;\n            case 'chat-lore':\n                matches = getChatLorebookMatches(searchTerm);\n                break;\n            default:\n                await showModal({ type: 'alert', title: '替换失败', text: '替换功能仅支持世界书视图。' });\n                return;\n        }\n        // 如果没有匹配项，提示用户\n        if (matches.length === 0) {\n            await showModal({ type: 'alert', title: '替换失败', text: '未找到匹配的条目。' });\n            return;\n        }\n        // 显示确认对话框\n        const confirmResult = await showModal({\n            type: 'confirm',\n            title: '确认替换',\n            text: `找到 ${matches.length} 个匹配项。\\n\\n确定要将 \"${searchTerm}\" 替换为 \"${replaceTerm}\" 吗？\\n\\n注意：此操作仅替换条目的关键词、内容和条目名称，不会替换世界书本身的名称。\\n此操作不可撤销，请谨慎操作。`,\n        });\n        // 如果用户确认替换，则执行替换\n        if (confirmResult) {\n            const progressToast = showProgressToast('正在执行替换...');\n            try {\n                await performReplace(matches, searchTerm, replaceTerm);\n                progressToast.remove();\n                showSuccessTick('替换完成');\n                // 刷新视图\n                renderContent();\n            }\n            catch (error) {\n                progressToast.remove();\n                console.error('[WorldInfoOptimizer] Replace error:', error);\n                await showModal({\n                    type: 'alert',\n                    title: '替换失败',\n                    text: '替换过程中发生错误，请检查开发者控制台获取详细信息。',\n                });\n            }\n        }\n    });\n    // 执行替换操作的函数\n    const performReplace = async (matches, searchTerm, replaceTerm) => {\n        // 创建一个映射来跟踪每个世界书的更改\n        const bookUpdates = new Map();\n        // 遍历所有匹配项\n        for (const match of matches) {\n            const { bookName, entry } = match;\n            let updated = false;\n            // 如果还没有为这个世界书创建更新数组，则创建一个\n            if (!bookUpdates.has(bookName)) {\n                bookUpdates.set(bookName, []);\n            }\n            // 创建条目的深拷贝以进行修改\n            const updatedEntry = JSON.parse(JSON.stringify(entry));\n            // 替换关键词\n            if (updatedEntry.keys && Array.isArray(updatedEntry.keys)) {\n                const newKeys = updatedEntry.keys.map((key) => key.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm));\n                // 检查是否有实际更改\n                if (JSON.stringify(updatedEntry.keys) !== JSON.stringify(newKeys)) {\n                    updatedEntry.keys = newKeys;\n                    updated = true;\n                }\n            }\n            // 替换条目内容\n            if (updatedEntry.content) {\n                const newContent = updatedEntry.content.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.content !== newContent) {\n                    updatedEntry.content = newContent;\n                    updated = true;\n                }\n            }\n            // 替换条目名称（comment）\n            if (updatedEntry.comment) {\n                const newComment = updatedEntry.comment.replace(new RegExp(searchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replaceTerm);\n                if (updatedEntry.comment !== newComment) {\n                    updatedEntry.comment = newComment;\n                    updated = true;\n                }\n            }\n            // 如果有更改，则将更新后的条目添加到更新数组中\n            if (updated) {\n                bookUpdates.get(bookName).push(updatedEntry);\n            }\n        }\n        // 应用所有更改\n        for (const [bookName, entriesToUpdate] of bookUpdates.entries()) {\n            if (entriesToUpdate.length > 0) {\n                // 调用TavernAPI来更新条目\n                const result = await TavernAPI.setLorebookEntries(bookName, entriesToUpdate);\n                if (result && result.entries) {\n                    // 更新本地状态\n                    safeSetLorebookEntries(bookName, result.entries);\n                }\n            }\n        }\n        // 等待一段时间以确保所有操作完成\n        await new Promise(resolve => setTimeout(resolve, 100));\n    };\n    // 获取匹配项的函数\n    const getGlobalLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const books = [...appState.allLorebooks].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.name.localeCompare(b.name));\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                entries.forEach(entry => {\n                    matches.push({ bookName: book.name, entry });\n                });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            books.forEach(book => {\n                const entries = [...safeGetLorebookEntries(book.name)];\n                const bookNameMatches = appState.searchFilters.bookName && book.name.toLowerCase().includes(searchTerm.toLowerCase());\n                entries.forEach(entry => {\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName: book.name, entry });\n                    }\n                });\n            });\n        }\n        return matches;\n    };\n    const getCharacterLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const linkedBooks = appState.lorebooks.character;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveCharacter = context.characterId !== undefined && context.characterId !== null;\n        if (!hasActiveCharacter || linkedBooks.length === 0) {\n            return matches;\n        }\n        linkedBooks.forEach(bookName => {\n            const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n            if (!searchTerm) {\n                // 如果没有搜索词，返回所有条目\n                entries.forEach(entry => {\n                    matches.push({ bookName, entry });\n                });\n            }\n            else {\n                // 根据搜索词和过滤器获取匹配项\n                entries.forEach(entry => {\n                    const bookNameMatches = appState.searchFilters.bookName && bookName.toLowerCase().includes(searchTerm.toLowerCase());\n                    const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                    const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                    const contentMatch = appState.searchFilters.content &&\n                        entry.content &&\n                        entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                    // 如果书名匹配，或者条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                    if (bookNameMatches || entryNameMatches || keywordsMatch || contentMatch) {\n                        matches.push({ bookName, entry });\n                    }\n                });\n            }\n        });\n        return matches;\n    };\n    const getChatLorebookMatches = (searchTerm) => {\n        const matches = [];\n        const bookName = appState.chatLorebook;\n        const context = parentWin.SillyTavern.getContext();\n        const hasActiveChat = context.chatId !== undefined && context.chatId !== null;\n        if (!hasActiveChat || !bookName) {\n            return matches;\n        }\n        const entries = [...safeGetLorebookEntries(bookName)].sort((a, b) => Number(b.enabled) - Number(a.enabled) || a.display_index - b.display_index);\n        if (!searchTerm) {\n            // 如果没有搜索词，返回所有条目\n            entries.forEach(entry => {\n                matches.push({ bookName, entry });\n            });\n        }\n        else {\n            // 根据搜索词和过滤器获取匹配项\n            entries.forEach(entry => {\n                const entryNameMatches = appState.searchFilters.entryName && (entry.comment || '').toLowerCase().includes(searchTerm.toLowerCase());\n                const keywordsMatch = appState.searchFilters.keywords && entry.keys.join(' ').toLowerCase().includes(searchTerm.toLowerCase());\n                const contentMatch = appState.searchFilters.content &&\n                    entry.content &&\n                    entry.content.toLowerCase().includes(searchTerm.toLowerCase());\n                // 如果条目名、关键词、内容中有任何一个匹配，则添加到匹配项中\n                if (entryNameMatches || keywordsMatch || contentMatch) {\n                    matches.push({ bookName, entry });\n                }\n            });\n        }\n        return matches;\n    };\n    // --- SortableJS 加载和拖拽排序功能 ---\n    const loadSortableJS = (callback) => {\n        if (parentWin.Sortable) {\n            callback();\n            return;\n        }\n        const script = parentWin.document.createElement('script');\n        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.2/Sortable.min.js';\n        script.onload = () => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded successfully.');\n            callback();\n        };\n        script.onerror = () => {\n            console.error('[WorldInfoOptimizer] Failed to load SortableJS.');\n            showModal({ type: 'alert', title: '错误', text: '无法加载拖拽排序库，请检查网络连接或浏览器控制台。' });\n        };\n        parentWin.document.head.appendChild(script);\n    };\n    // 防抖函数\n    const debounce = (func, delay) => {\n        let timeout;\n        return (...args) => {\n            clearTimeout(timeout);\n            timeout = setTimeout(() => func(...args), delay);\n        };\n    };\n    // 防抖保存正则顺序\n    const debouncedSaveRegexOrder = debounce(errorCatched(async () => {\n        const allRegexes = [...appState.regexes.global, ...appState.regexes.character];\n        await TavernAPI.replaceRegexes(allRegexes.filter(r => r.source !== 'card'));\n        await TavernAPI.saveSettings();\n        showSuccessTick('正则顺序已保存');\n    }), 800);\n    // 处理正则拖拽结束事件\n    const handleRegexDragEnd = errorCatched(async (evt, scope) => {\n        const { oldIndex, newIndex } = evt;\n        if (oldIndex === newIndex)\n            return;\n        const targetList = appState.regexes[scope];\n        const [movedItem] = targetList.splice(oldIndex, 1);\n        targetList.splice(newIndex, 0, movedItem);\n        // 乐观更新UI：重新渲染序号\n        renderContent();\n        // 防抖保存\n        debouncedSaveRegexOrder();\n    });\n    // --- 主程序逻辑 ---\n    function main(jquery, tavernHelper) {\n        $ = jquery;\n        TavernHelper = tavernHelper;\n        console.log('[WorldInfoOptimizer] Initializing main application...');\n        // 初始化 TavernAPI\n        initializeTavernAPI();\n        // 加载 SortableJS 然后初始化 UI\n        loadSortableJS(() => {\n            console.log('[WorldInfoOptimizer] SortableJS loaded, creating UI elements...');\n            // 创建主面板\n            createMainPanel();\n            // 创建扩展菜单按钮\n            createExtensionButton();\n            // 绑定事件处理器\n            bindEventHandlers();\n            // 加载初始数据\n            loadAllData();\n            console.log('[WorldInfoOptimizer] Main application initialized successfully.');\n        });\n    }\n    // --- UI 创建函数 ---\n    const createMainPanel = () => {\n        const parentDoc = parentWin.document;\n        // 检查面板是否已存在\n        if ($(`#${PANEL_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Panel already exists, skipping creation.');\n            return;\n        }\n        const panelHtml = `\r\n            <div id=\"${PANEL_ID}\" class=\"wio-panel\" style=\"display: none;\">\r\n                <div class=\"wio-panel-header\">\r\n                    <h3 class=\"wio-panel-title\">\r\n                        <i class=\"fa-solid fa-book\"></i> 世界书优化器\r\n                    </h3>\r\n                    <div class=\"wio-panel-controls\">\r\n                        <button id=\"${REFRESH_BTN_ID}\" class=\"wio-btn wio-btn-icon\" title=\"刷新数据\">\r\n                            <i class=\"fa-solid fa-sync-alt\"></i>\r\n                        </button>\r\n                        <button class=\"wio-btn wio-btn-icon wio-panel-close\" title=\"关闭\">\r\n                            <i class=\"fa-solid fa-times\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"wio-panel-body\">\r\n                    <div class=\"wio-tabs\">\r\n                        <button class=\"wio-tab-btn active\" data-tab=\"global-lore\">全局世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-lore\">角色世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"chat-lore\">聊天世界书</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"global-regex\">全局正则</button>\r\n                        <button class=\"wio-tab-btn\" data-tab=\"char-regex\">角色正则</button>\r\n                    </div>\r\n                    <div class=\"wio-search-section\">\r\n                        <div class=\"wio-search-bar\">\r\n                            <input type=\"text\" id=\"${SEARCH_INPUT_ID}\" placeholder=\"搜索世界书、条目、关键词...\" class=\"wio-search-input\">\r\n                            <input type=\"text\" id=\"wio-replace-input\" placeholder=\"替换为...\" class=\"wio-search-input\">\r\n                            <button id=\"wio-replace-btn\" class=\"wio-btn wio-search-btn\" title=\"替换\">\r\n                                <i class=\"fa-solid fa-exchange-alt\"></i>\r\n                            </button>\r\n                        </div>\r\n                        <div id=\"wio-search-filters-container\" class=\"wio-search-filters\">\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-book-name\" checked> 书名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-entry-name\" checked> 条目名</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-keywords\" checked> 关键词</label>\r\n                            <label><input type=\"checkbox\" id=\"wio-filter-content\" checked> 内容</label>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"wio-toolbar\">\r\n                        <button id=\"${CREATE_LOREBOOK_BTN_ID}\" class=\"wio-btn wio-btn-primary\">\r\n                            <i class=\"fa-solid fa-plus\"></i> 新建世界书\r\n                        </button>\r\n                        <button id=\"${COLLAPSE_ALL_BTN_ID}\" class=\"wio-btn\">\r\n                            <i class=\"fa-solid fa-compress-alt\"></i> 全部折叠\r\n                        </button>\r\n                        <button class=\"wio-btn wio-multi-select-toggle\">\r\n                            <i class=\"fa-solid fa-check-square\"></i> 多选模式\r\n                        </button>\r\n                        <div id=\"wio-multi-select-controls\" class=\"wio-multi-select-controls\" style=\"display: none;\">\r\n                            <div class=\"wio-multi-select-actions\">\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-all-btn\">全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-none-btn\">取消全选</button>\r\n                                <button class=\"wio-multi-select-action-btn\" id=\"wio-select-invert-btn\">反选</button>\r\n                                <button class=\"wio-multi-select-action-btn enable\" id=\"wio-batch-enable-btn\">批量启用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-disable-btn\">批量禁用</button>\r\n                                <button class=\"wio-multi-select-action-btn disable\" id=\"wio-batch-delete-btn\">批量删除</button>\r\n                                <span class=\"wio-selection-count\" id=\"wio-selection-count\">已选择: 0</span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div id=\"${PANEL_ID}-content\" class=\"wio-content\">\r\n                        <p class=\"wio-info-text\">正在初始化...</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        `;\n        $('body', parentDoc).append(panelHtml);\n        // 添加基础样式\n        addBasicStyles();\n    };\n    const createExtensionButton = () => {\n        const parentDoc = parentWin.document;\n        // 检查按钮是否已存在\n        if ($(`#${BUTTON_ID}`, parentDoc).length > 0) {\n            console.log('[WorldInfoOptimizer] Extension button already exists, skipping creation.');\n            return;\n        }\n        const buttonHtml = `\r\n            <div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5 interactable\" title=\"${BUTTON_TOOLTIP}\">\r\n                <div class=\"fa-solid fa-book extensionsMenuExtensionButton\" title=\"${BUTTON_TOOLTIP}\"></div>\r\n                <span>${BUTTON_TEXT_IN_MENU}</span>\r\n            </div>\r\n        `;\n        const $extensionsMenu = $('#extensionsMenu', parentDoc);\n        if ($extensionsMenu.length > 0) {\n            $extensionsMenu.append(buttonHtml);\n            console.log(`[WorldInfoOptimizer] Button #${BUTTON_ID} appended to #extensionsMenu.`);\n        }\n        else {\n            console.warn('[WorldInfoOptimizer] Extensions menu not found, cannot add button.');\n        }\n    };\n    const addBasicStyles = () => {\n        const parentDoc = parentWin.document;\n        // 检查样式是否已添加\n        if ($('#wio-basic-styles', parentDoc).length > 0) {\n            return;\n        }\n        const basicStyles = `\r\n            <style id=\"wio-basic-styles\">\r\n                .wio-panel {\r\n                    position: fixed;\r\n                    top: 50%;\r\n                    left: 50%;\r\n                    transform: translate(-50%, -50%);\r\n                    width: 90%;\r\n                    max-width: 1200px;\r\n                    height: 80%;\r\n                    background: #2a2a2a;\r\n                    border: 1px solid #444;\r\n                    border-radius: 8px;\r\n                    box-shadow: 0 4px 20px rgba(0,0,0,0.5);\r\n                    z-index: 10000;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                    border-radius: 8px 8px 0 0;\r\n                }\r\n                .wio-panel-title {\r\n                    margin: 0;\r\n                    font-size: 18px;\r\n                    color: #fff;\r\n                }\r\n                .wio-panel-controls {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                }\r\n                .wio-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-btn:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-btn-primary {\r\n                    background: #007bff;\r\n                }\r\n                .wio-btn-primary:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-btn-icon {\r\n                    padding: 8px;\r\n                    width: 36px;\r\n                    height: 36px;\r\n                }\r\n                .wio-panel-body {\r\n                    flex: 1;\r\n                    display: flex;\r\n                    flex-direction: column;\r\n                    overflow: hidden;\r\n                }\r\n                .wio-tabs {\r\n                    display: flex;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #333;\r\n                }\r\n                .wio-tab-btn {\r\n                    padding: 12px 20px;\r\n                    border: none;\r\n                    background: transparent;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                    border-bottom: 2px solid transparent;\r\n                    transition: all 0.2s;\r\n                }\r\n                .wio-tab-btn:hover {\r\n                    background: #444;\r\n                    color: #fff;\r\n                }\r\n                .wio-tab-btn.active {\r\n                    color: #fff;\r\n                    border-bottom-color: #007bff;\r\n                    background: #444;\r\n                }\r\n                .wio-search-section {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-search-bar {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    margin-bottom: 10px;\r\n                }\r\n                .wio-search-input {\r\n                    flex: 1;\r\n                    padding: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-search-filters {\r\n                    display: flex;\r\n                    gap: 15px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-search-filters label {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    gap: 5px;\r\n                    color: #ccc;\r\n                    cursor: pointer;\r\n                }\r\n                .wio-toolbar {\r\n                    padding: 15px 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    background: #2a2a2a;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-content {\r\n                    flex: 1;\r\n                    padding: 20px;\r\n                    overflow-y: auto;\r\n                    background: #1a1a1a;\r\n                }\r\n                .wio-info-text {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 40px 0;\r\n                }\r\n                .wio-book-item {\r\n                    margin-bottom: 20px;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px;\r\n                    border: 1px solid #444;\r\n                }\r\n                .wio-highlight {\r\n                    background: #ffeb3b;\r\n                    color: #000;\r\n                    padding: 1px 2px;\r\n                    border-radius: 2px;\r\n                }\r\n                .wio-item-container {\r\n                    margin-bottom: 10px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                }\r\n                .wio-item-container.enabled {\r\n                    border-left: 3px solid #28a745;\r\n                }\r\n                .wio-item-container.selected {\r\n                    background: #2a4a6b;\r\n                    border-color: #007bff;\r\n                }\r\n                .wio-item-header {\r\n                    display: flex;\r\n                    align-items: center;\r\n                    padding: 10px 15px;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-item-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-item-name {\r\n                    flex: 1;\r\n                    margin-left: 10px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-item-controls {\r\n                    display: flex;\r\n                    gap: 5px;\r\n                }\r\n                .wio-action-btn-icon {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #555;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn-icon:hover {\r\n                    background: #666;\r\n                }\r\n                .wio-toggle-btn {\r\n                    padding: 4px 8px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #dc3545;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-toggle-btn:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-book-group {\r\n                    margin-bottom: 20px;\r\n                    border: 1px solid #444;\r\n                    border-radius: 6px;\r\n                    background: #2a2a2a;\r\n                }\r\n                .wio-book-group-header, .wio-global-book-header {\r\n                    display: flex;\r\n                    justify-content: space-between;\r\n                    align-items: center;\r\n                    padding: 15px;\r\n                    background: #333;\r\n                    border-radius: 6px 6px 0 0;\r\n                    cursor: pointer;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-book-group-header:hover, .wio-global-book-header:hover {\r\n                    background: #444;\r\n                }\r\n                .wio-book-name {\r\n                    font-size: 16px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status {\r\n                    margin-left: 10px;\r\n                    padding: 2px 8px;\r\n                    border-radius: 12px;\r\n                    font-size: 12px;\r\n                    font-weight: 500;\r\n                }\r\n                .wio-book-status.enabled {\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                }\r\n                .wio-book-status.disabled {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-entry-actions, .wio-regex-actions {\r\n                    padding: 15px;\r\n                    border-bottom: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                }\r\n                .wio-action-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-action-btn:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-multi-select-controls {\r\n                    margin-top: 10px;\r\n                    padding: 10px;\r\n                    background: #333;\r\n                    border-radius: 4px;\r\n                }\r\n                .wio-multi-select-actions {\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    flex-wrap: wrap;\r\n                    align-items: center;\r\n                }\r\n                .wio-multi-select-action-btn {\r\n                    padding: 6px 12px;\r\n                    border: none;\r\n                    border-radius: 3px;\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                    cursor: pointer;\r\n                    font-size: 12px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-multi-select-action-btn:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-multi-select-action-btn.enable {\r\n                    background: #28a745;\r\n                }\r\n                .wio-multi-select-action-btn.enable:hover {\r\n                    background: #218838;\r\n                }\r\n                .wio-multi-select-action-btn.disable {\r\n                    background: #dc3545;\r\n                }\r\n                .wio-multi-select-action-btn.disable:hover {\r\n                    background: #c82333;\r\n                }\r\n                .wio-selection-count {\r\n                    margin-left: auto;\r\n                    color: #ccc;\r\n                    font-size: 12px;\r\n                }\r\n                .wio-info-text-small {\r\n                    text-align: center;\r\n                    color: #888;\r\n                    font-style: italic;\r\n                    margin: 20px 0;\r\n                    font-size: 14px;\r\n                }\r\n                .wio-used-by-chars {\r\n                    margin-top: 5px;\r\n                    font-size: 12px;\r\n                    color: #aaa;\r\n                }\r\n                .wio-used-by-chars span {\r\n                    background: #555;\r\n                    padding: 2px 6px;\r\n                    border-radius: 3px;\r\n                    margin-right: 5px;\r\n                }\r\n                .wio-toast-notification {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #28a745;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-toast-notification.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-progress-toast {\r\n                    position: fixed;\r\n                    top: 20px;\r\n                    right: 20px;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    padding: 12px 20px;\r\n                    border-radius: 6px;\r\n                    box-shadow: 0 4px 12px rgba(0,0,0,0.3);\r\n                    z-index: 10001;\r\n                    opacity: 0;\r\n                    transform: translateX(100%);\r\n                    transition: all 0.3s ease;\r\n                }\r\n                .wio-progress-toast.visible {\r\n                    opacity: 1;\r\n                    transform: translateX(0);\r\n                }\r\n                .wio-modal-overlay {\r\n                    position: fixed;\r\n                    top: 0;\r\n                    left: 0;\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    background: rgba(0,0,0,0.7);\r\n                    z-index: 10002;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    justify-content: center;\r\n                }\r\n                .wio-modal-content {\r\n                    background: #2a2a2a;\r\n                    border-radius: 8px;\r\n                    max-width: 500px;\r\n                    width: 90%;\r\n                    max-height: 80vh;\r\n                    overflow-y: auto;\r\n                }\r\n                .wio-modal-header {\r\n                    padding: 20px;\r\n                    border-bottom: 1px solid #444;\r\n                    font-size: 18px;\r\n                    font-weight: 600;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-body {\r\n                    padding: 20px;\r\n                    color: #ccc;\r\n                }\r\n                .wio-modal-input {\r\n                    width: 100%;\r\n                    padding: 10px;\r\n                    margin-top: 10px;\r\n                    border: 1px solid #555;\r\n                    border-radius: 4px;\r\n                    background: #333;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-input.wio-input-error {\r\n                    border-color: #dc3545;\r\n                    animation: shake 0.5s;\r\n                }\r\n                @keyframes shake {\r\n                    0%, 100% { transform: translateX(0); }\r\n                    25% { transform: translateX(-5px); }\r\n                    75% { transform: translateX(5px); }\r\n                }\r\n                .wio-modal-footer {\r\n                    padding: 20px;\r\n                    border-top: 1px solid #444;\r\n                    display: flex;\r\n                    gap: 10px;\r\n                    justify-content: flex-end;\r\n                }\r\n                .wio-modal-btn {\r\n                    padding: 8px 16px;\r\n                    border: none;\r\n                    border-radius: 4px;\r\n                    cursor: pointer;\r\n                    font-size: 14px;\r\n                    transition: background 0.2s;\r\n                }\r\n                .wio-modal-ok {\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-ok:hover {\r\n                    background: #0056b3;\r\n                }\r\n                .wio-modal-cancel {\r\n                    background: #6c757d;\r\n                    color: #fff;\r\n                }\r\n                .wio-modal-cancel:hover {\r\n                    background: #5a6268;\r\n                }\r\n                .wio-drag-handle {\r\n                    cursor: grab;\r\n                    color: #ccc;\r\n                    margin-right: 10px;\r\n                    padding: 0 5px;\r\n                    opacity: 0.6;\r\n                    transition: opacity 0.2s;\r\n                }\r\n                .wio-drag-handle:hover {\r\n                    opacity: 1;\r\n                }\r\n                .wio-drag-handle:active {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-item-container.sortable-ghost {\r\n                    opacity: 0.4;\r\n                    background: #2a4a6b;\r\n                }\r\n                .wio-item-container.sortable-chosen {\r\n                    cursor: grabbing;\r\n                }\r\n                .wio-order-indicator {\r\n                    display: inline-block;\r\n                    background: #007bff;\r\n                    color: #fff;\r\n                    font-size: 10px;\r\n                    font-weight: bold;\r\n                    padding: 2px 6px;\r\n                    border-radius: 10px;\r\n                    margin-right: 8px;\r\n                    min-width: 20px;\r\n                    text-align: center;\r\n                }\r\n                .wio-regex-list {\r\n                    padding: 15px;\r\n                }\r\n                /* 按钮激活状态 */\r\n                #${BUTTON_ID}.active {\r\n                    background-color: rgba(126, 183, 213, 0.3) !important;\r\n                    border-color: #7eb7d5 !important;\r\n                }\r\n                #${BUTTON_ID}:hover {\r\n                    background-color: rgba(126, 183, 213, 0.15);\r\n                }\r\n                /* 重命名UI样式 */\r\n                .wio-rename-ui {\r\n                    position: absolute;\r\n                    top: 0;\r\n                    left: 0;\r\n                    right: 0;\r\n                    bottom: 0;\r\n                    display: flex;\r\n                    align-items: center;\r\n                    background-color: #1a1a1a;\r\n                    padding: 6px 15px;\r\n                }\r\n                .wio-rename-input-wrapper {\r\n                    position: relative;\r\n                    flex-grow: 1;\r\n                }\r\n                .wio-rename-input {\r\n                    width: 100%;\r\n                    height: 100%;\r\n                    box-sizing: border-box;\r\n                    padding: 6px 64px 6px 8px;\r\n                    border-radius: 6px;\r\n                    font-weight: 600;\r\n                    color: #2C3E50;\r\n                    background-color: #fff;\r\n                    border: 1px solid #007bff;\r\n                    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\r\n                    outline: none;\r\n                }\r\n                .wio-rename-ui .wio-action-btn-icon {\r\n                    position: absolute;\r\n                    top: 50%;\r\n                    transform: translateY(-50%);\r\n                    width: 24px;\r\n                    height: 24px;\r\n                    font-size: 0.9em;\r\n                }\r\n                .wio-rename-save-btn {\r\n                    right: 32px;\r\n                    color: #28a745;\r\n                }\r\n                .wio-rename-cancel-btn {\r\n                    right: 4px;\r\n                    color: #dc3545;\r\n                }\r\n            </style>\r\n        `;\n        $('head', parentDoc).append(basicStyles);\n    };\n    // --- 面板显示/隐藏函数 ---\n    const hidePanel = () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.hide();\n        $(`#${BUTTON_ID}`, parentDoc).removeClass('active');\n        $parentBody.off('mousedown.wio-outside-click');\n    };\n    const showPanel = async () => {\n        const parentDoc = parentWin.document;\n        const $panel = $(`#${PANEL_ID}`, parentDoc);\n        const $parentBody = $('body', parentDoc);\n        $panel.css('display', 'flex');\n        $(`#${BUTTON_ID}`, parentDoc).addClass('active');\n        // 点击外部关闭面板\n        $parentBody.on('mousedown.wio-outside-click', function (event) {\n            if ($(event.target).closest(`#${PANEL_ID}`).length === 0 &&\n                $(event.target).closest(`#${BUTTON_ID}`).length === 0) {\n                hidePanel();\n            }\n        });\n        if (!appState.isDataLoaded) {\n            await loadAllData();\n        }\n        else {\n            renderContent();\n        }\n    };\n    // --- 按钮事件处理函数 ---\n    const handleHeaderClick = errorCatched(async (event) => {\n        const $target = $(event.target);\n        const $container = $(event.currentTarget).closest('.wio-item-container, .wio-book-group');\n        // 如果点击的是按钮等可交互控件，则不执行后续逻辑\n        if ($target.closest('.wio-item-controls, .wio-rename-ui').length > 0) {\n            return;\n        }\n        // 如果处于多选模式\n        if (appState.multiSelectMode) {\n            let itemKey;\n            const isGlobalLoreTab = appState.activeTab === 'global-lore';\n            const isBookHeader = $container.hasClass('wio-book-group');\n            if (isGlobalLoreTab && isBookHeader) {\n                const isEditingEntries = $container.hasClass('editing-entries');\n                if (!isEditingEntries) {\n                    const bookName = $container.data('book-name');\n                    itemKey = `book:${bookName}`;\n                }\n            }\n            else if ($container.hasClass('wio-item-container')) {\n                const canSelectItem = isGlobalLoreTab\n                    ? $container.closest('.wio-book-group').hasClass('editing-entries')\n                    : true;\n                if (canSelectItem) {\n                    const itemType = $container.data('type');\n                    const itemId = $container.data('id');\n                    if (itemType === 'lore') {\n                        const bookName = $container.data('book-name');\n                        itemKey = `lore:${bookName}:${itemId}`;\n                    }\n                    else {\n                        itemKey = `regex:${itemId}`;\n                    }\n                }\n            }\n            if (itemKey) {\n                if (appState.selectedItems.has(itemKey)) {\n                    appState.selectedItems.delete(itemKey);\n                    $container.removeClass('selected');\n                }\n                else {\n                    appState.selectedItems.add(itemKey);\n                    $container.addClass('selected');\n                }\n                updateSelectionCount();\n            }\n        }\n        else {\n            // 非多选模式：展开/折叠内容\n            const $content = $container.find('.wio-collapsible-content');\n            if ($content.length > 0) {\n                $content.slideToggle(200);\n                $container.toggleClass('collapsed');\n            }\n        }\n    });\n    const handleToggleState = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $button = $(event.currentTarget);\n        const $elementToSort = $button.closest('.wio-book-group, .wio-item-container');\n        if ($elementToSort.hasClass('renaming'))\n            return;\n        const isEnabling = !$elementToSort.hasClass('enabled');\n        const parentList = $elementToSort.parent();\n        if ($button.hasClass('wio-global-toggle')) {\n            const bookName = $elementToSort.data('book-name');\n            const settings = await TavernAPI.getLorebookSettings();\n            const currentBooks = new Set(settings.selected_global_lorebooks || []);\n            if (isEnabling)\n                currentBooks.add(bookName);\n            else\n                currentBooks.delete(bookName);\n            await TavernAPI.setLorebookSettings({ selected_global_lorebooks: Array.from(currentBooks) });\n            await TavernAPI.saveSettings();\n            const bookState = appState.allLorebooks.find(b => b.name === bookName);\n            if (bookState)\n                bookState.enabled = isEnabling;\n        }\n        else {\n            const type = $elementToSort.data('type');\n            const id = $elementToSort.data('id');\n            if (type === 'lore') {\n                const bookName = $elementToSort.data('book-name');\n                await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), enabled: isEnabling }]);\n                const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));\n                if (entry)\n                    entry.enabled = isEnabling;\n            }\n            else {\n                const allServerRegexes = await TavernAPI.getRegexes();\n                const regex = allServerRegexes.find((r) => r.id === id);\n                if (regex) {\n                    regex.enabled = isEnabling;\n                    await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                    await TavernAPI.saveSettings();\n                    const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                        appState.regexes.character.find((r) => r.id === id);\n                    if (localRegex)\n                        localRegex.enabled = isEnabling;\n                }\n            }\n        }\n        showSuccessTick(isEnabling ? '已启用' : '已禁用');\n        $elementToSort.toggleClass('enabled', isEnabling);\n        // 重新排序：启用的项目排在前面\n        const items = parentList.children().get();\n        items.sort((a, b) => {\n            const aEnabled = $(a).hasClass('enabled');\n            const bEnabled = $(b).hasClass('enabled');\n            if (aEnabled !== bEnabled)\n                return bEnabled ? 1 : -1;\n            const aName = $(a).find('.wio-item-name').text().trim();\n            const bName = $(b).find('.wio-item-name').text().trim();\n            return aName.localeCompare(bName);\n        });\n        parentList.append(items);\n    });\n    const handleRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        if ($container.hasClass('renaming') || $container.length === 0)\n            return;\n        const $header = $container.find('.wio-item-header').first();\n        const $nameSpan = $header.find('.wio-item-name').first();\n        const oldName = $nameSpan.clone().children().remove().end().text().trim();\n        const renameUIHtml = `<div class=\"wio-rename-ui\"><div class=\"wio-rename-input-wrapper\"><input type=\"text\" class=\"wio-rename-input\" value=\"${escapeHtml(oldName)}\" /><button class=\"wio-action-btn-icon wio-rename-save-btn\" title=\"确认\"><i class=\"fa-solid fa-check\"></i></button><button class=\"wio-action-btn-icon wio-rename-cancel-btn\" title=\"取消\"><i class=\"fa-solid fa-times\"></i></button></div></div>`;\n        $container.addClass('renaming');\n        $header.append(renameUIHtml);\n        $header.find('.wio-rename-input').focus().select();\n    });\n    const exitRenameMode = ($container, newName = null) => {\n        const $header = $container.find('.wio-item-header').first();\n        const $nameSpan = $header.find('.wio-item-name').first();\n        if (newName) {\n            $nameSpan.text(newName);\n        }\n        $header.find('.wio-rename-ui').remove();\n        $container.removeClass('renaming');\n    };\n    const handleConfirmRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        const $input = $container.find('.wio-rename-input');\n        const newName = $input.val().trim();\n        const oldName = $container.find('.wio-item-name').first().text().trim();\n        if (!newName || newName === oldName) {\n            exitRenameMode($container, oldName);\n            return;\n        }\n        const type = $container.data('type');\n        const id = $container.data('id');\n        if (type === 'lore') {\n            const bookName = $container.data('book-name');\n            await TavernAPI.setLorebookEntries(bookName, [{ uid: Number(id), comment: newName }]);\n            const entry = safeGetLorebookEntries(bookName).find((e) => e.uid === Number(id));\n            if (entry)\n                entry.comment = newName;\n        }\n        else {\n            // type === 'regex'\n            const allServerRegexes = await TavernAPI.getRegexes();\n            const regex = allServerRegexes.find((r) => r.id === id);\n            if (regex) {\n                regex.script_name = newName;\n                await TavernAPI.replaceRegexes(allServerRegexes.filter((r) => r.source !== 'card'));\n                await TavernAPI.saveSettings();\n                const localRegex = appState.regexes.global.find((r) => r.id === id) ||\n                    appState.regexes.character.find((r) => r.id === id);\n                if (localRegex)\n                    localRegex.script_name = newName;\n            }\n        }\n        exitRenameMode($container, newName);\n        showSuccessTick('重命名成功');\n    });\n    const handleCancelRename = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $container = $(event.currentTarget).closest('.wio-item-container');\n        exitRenameMode($container);\n    });\n    const handleRenameKeydown = errorCatched(async (event) => {\n        if (event.key === 'Enter') {\n            $(event.currentTarget).siblings('.wio-rename-save-btn').click();\n        }\n        else if (event.key === 'Escape') {\n            $(event.currentTarget).siblings('.wio-rename-cancel-btn').click();\n        }\n    });\n    const handleDeleteEntry = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $item = $(event.currentTarget).closest('.wio-item-container');\n        const bookName = $item.data('book-name');\n        const uid = Number($item.data('id'));\n        const entryName = $item.find('.wio-item-name').text().trim();\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '确认删除',\n            text: `确定要删除条目\"${entryName}\"吗？此操作不可撤销。`,\n        });\n        if (confirmed) {\n            await TavernAPI.deleteLorebookEntries(bookName, [uid.toString()]);\n            const entries = safeGetLorebookEntries(bookName);\n            const index = entries.findIndex((e) => e.uid === uid);\n            if (index !== -1)\n                entries.splice(index, 1);\n            $item.remove();\n            showSuccessTick('条目已删除');\n        }\n    });\n    const handleDeleteBook = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');\n        const bookName = $bookGroup.data('book-name');\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '确认删除',\n            text: `确定要删除世界书\"${bookName}\"吗？此操作不可撤销。`,\n        });\n        if (confirmed) {\n            await TavernAPI.deleteLorebook(bookName);\n            const index = appState.allLorebooks.findIndex(b => b.name === bookName);\n            if (index !== -1)\n                appState.allLorebooks.splice(index, 1);\n            appState.lorebookEntries.delete(bookName);\n            $bookGroup.remove();\n            showSuccessTick('世界书已删除');\n        }\n    });\n    const handleRenameBook = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $bookGroup = $(event.currentTarget).closest('.wio-book-group');\n        const oldName = $bookGroup.data('book-name');\n        if (!oldName)\n            return;\n        const newName = await showModal({\n            type: 'prompt',\n            title: '重命名世界书',\n            text: '请输入新的世界书名称：',\n            value: oldName,\n        });\n        if (newName && newName !== oldName) {\n            // 创建新世界书\n            await TavernAPI.createLorebook(newName);\n            // 复制条目\n            const entries = safeGetLorebookEntries(oldName);\n            if (entries.length > 0) {\n                await TavernAPI.createLorebookEntries(newName, entries);\n            }\n            // 删除旧世界书\n            await TavernAPI.deleteLorebook(oldName);\n            // 更新状态\n            const bookIndex = appState.allLorebooks.findIndex(b => b.name === oldName);\n            if (bookIndex !== -1) {\n                appState.allLorebooks[bookIndex].name = newName;\n            }\n            appState.lorebookEntries.set(newName, entries);\n            appState.lorebookEntries.delete(oldName);\n            showSuccessTick('世界书重命名成功');\n            renderContent();\n        }\n    });\n    const handleCreateEntry = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entryName = await showModal({\n            type: 'prompt',\n            title: '新建条目',\n            text: '请输入条目名称：',\n            value: '新条目',\n        });\n        if (entryName) {\n            const newEntry = {\n                uid: Date.now().toString(),\n                comment: entryName,\n                keys: [entryName],\n                content: '',\n                enabled: true,\n                insertion_order: 100,\n                case_sensitive: false,\n                name: entryName,\n                priority: 400,\n                id: Date.now(),\n                key: [entryName],\n                keysecondary: [],\n                selective: true,\n                constant: false,\n                vectorized: false,\n                selectiveLogic: 0,\n                addMemo: false,\n                order: 100,\n                position: 0,\n                disable: false,\n                excludeRecursion: false,\n                delayUntilRecursion: false,\n                display_index: 0,\n                forceActivation: false,\n                automationId: '',\n                role: 0,\n                scanDepth: null,\n                caseSensitive: false,\n                matchWholeWords: false,\n                useGroupScoring: false,\n                groupOverride: false,\n                groupWeight: 100,\n                sticky: 0,\n                cooldown: 0,\n                delay: 0,\n            };\n            await TavernAPI.createLorebookEntries(bookName, [newEntry]);\n            const entries = safeGetLorebookEntries(bookName);\n            entries.push(newEntry);\n            showSuccessTick('条目创建成功');\n            renderContent();\n        }\n    });\n    const handleBatchSetRecursion = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entries = [...safeGetLorebookEntries(bookName)];\n        if (!entries || entries.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '批量设置防递归',\n            text: `确定要为\"${bookName}\"中的所有 ${entries.length} 个条目开启\"防止递归\"吗？`,\n        });\n        if (confirmed) {\n            const updatedEntries = entries.map((entry) => ({\n                uid: entry.uid,\n                excludeRecursion: true,\n            }));\n            await TavernAPI.setLorebookEntries(bookName, updatedEntries);\n            // 更新本地状态\n            entries.forEach((entry) => {\n                entry.excludeRecursion = true;\n            });\n            showSuccessTick('已为所有条目开启\"防止递归\"');\n        }\n    });\n    const handleFixKeywords = errorCatched(async (event) => {\n        const bookName = $(event.currentTarget).data('book-name');\n        const entries = [...safeGetLorebookEntries(bookName)];\n        if (!entries || entries.length === 0) {\n            await showModal({ type: 'alert', title: '提示', text: '该世界书没有条目可操作。' });\n            return;\n        }\n        const confirmed = await showModal({\n            type: 'confirm',\n            title: '修复关键词',\n            text: `确定要修复\"${bookName}\"中所有条目的关键词格式吗？这将清理重复和空白关键词。`,\n        });\n        if (confirmed) {\n            let fixedCount = 0;\n            const updatedEntries = entries\n                .map((entry) => {\n                const originalKeys = entry.keys || [];\n                const cleanedKeys = [\n                    ...new Set(originalKeys.map((key) => key.trim()).filter((key) => key.length > 0)),\n                ];\n                if (JSON.stringify(originalKeys) !== JSON.stringify(cleanedKeys)) {\n                    fixedCount++;\n                    return {\n                        uid: entry.uid,\n                        keys: cleanedKeys,\n                    };\n                }\n                return null;\n            })\n                .filter(Boolean);\n            if (updatedEntries.length > 0) {\n                await TavernAPI.setLorebookEntries(bookName, updatedEntries);\n                // 更新本地状态\n                entries.forEach((entry) => {\n                    const update = updatedEntries.find((u) => u.uid === entry.uid);\n                    if (update) {\n                        entry.keys = update.keys;\n                    }\n                });\n            }\n            showSuccessTick(`已修复 ${fixedCount} 个条目的关键词`);\n        }\n    });\n    const handleEditEntriesToggle = errorCatched(async (event) => {\n        event.stopPropagation();\n        const $button = $(event.currentTarget);\n        const $bookGroup = $button.closest('.wio-book-group');\n        const isEditing = $bookGroup.hasClass('editing-entries');\n        if (isEditing) {\n            $bookGroup.removeClass('editing-entries');\n            $button.attr('title', '编辑/选择条目').find('i').removeClass('fa-check-square').addClass('fa-pen-to-square');\n            $button.removeClass('active');\n        }\n        else {\n            $bookGroup.addClass('editing-entries');\n            $button.attr('title', '完成编辑').find('i').removeClass('fa-pen-to-square').addClass('fa-check-square');\n            $button.addClass('active');\n        }\n    });\n    // --- 事件处理器 ---\n    const bindEventHandlers = () => {\n        const parentDoc = parentWin.document;\n        // 扩展菜单按钮点击事件\n        $(parentDoc).on('click', `#${BUTTON_ID}`, async () => {\n            const $panel = $(`#${PANEL_ID}`, parentDoc);\n            if ($panel.is(':visible')) {\n                hidePanel();\n            }\n            else {\n                await showPanel();\n            }\n        });\n        // 面板关闭按钮\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-panel-close`, () => {\n            hidePanel();\n        });\n        // 刷新按钮\n        $(parentDoc).on('click', `#${REFRESH_BTN_ID}`, () => {\n            loadAllData();\n        });\n        // 标签页切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-tab-btn`, (event) => {\n            const $this = $(event.currentTarget);\n            const tabId = $this.data('tab');\n            $(`#${PANEL_ID} .wio-tab-btn`, parentDoc).removeClass('active');\n            $this.addClass('active');\n            appState.activeTab = tabId;\n            renderContent();\n        });\n        // 搜索输入\n        $(parentDoc).on('input', `#${SEARCH_INPUT_ID}`, () => {\n            renderContent();\n        });\n        // 搜索过滤器\n        $(parentDoc).on('change', `#${PANEL_ID} .wio-search-filters input[type=\"checkbox\"]`, () => {\n            renderContent();\n        });\n        // 替换按钮\n        $(parentDoc).on('click', '#wio-replace-btn', () => {\n            handleReplace();\n        });\n        // 新建世界书按钮\n        $(parentDoc).on('click', `#${CREATE_LOREBOOK_BTN_ID}`, async () => {\n            try {\n                const bookName = await showModal({\n                    type: 'prompt',\n                    title: '新建世界书',\n                    text: '请输入世界书名称：',\n                    placeholder: '世界书名称',\n                });\n                if (bookName && typeof bookName === 'string') {\n                    const progressToast = showProgressToast('正在创建世界书...');\n                    await TavernAPI.createLorebook(bookName.trim());\n                    progressToast.remove();\n                    showSuccessTick(`世界书 \"${bookName}\" 创建成功`);\n                    loadAllData(); // 重新加载数据\n                }\n            }\n            catch (error) {\n                console.error('[WorldInfoOptimizer] Error creating lorebook:', error);\n            }\n        });\n        // 全部折叠按钮\n        $(parentDoc).on('click', `#${COLLAPSE_ALL_BTN_ID}`, () => {\n            $(`#${PANEL_ID} .wio-book-item`, parentDoc).addClass('collapsed');\n        });\n        // 多选模式切换\n        $(parentDoc).on('click', `#${PANEL_ID} .wio-multi-select-toggle`, (event) => {\n            appState.multiSelectMode = !appState.multiSelectMode;\n            const $this = $(event.currentTarget);\n            if (appState.multiSelectMode) {\n                $this.addClass('active').html('<i class=\"fa-solid fa-times\"></i> 退出多选');\n            }\n            else {\n                $this.removeClass('active').html('<i class=\"fa-solid fa-check-square\"></i> 多选模式');\n                appState.selectedItems.clear();\n            }\n            renderContent();\n        });\n        // ESC键关闭面板\n        $(parentDoc).on('keydown', (e) => {\n            if (e.key === 'Escape') {\n                const $panel = $(`#${PANEL_ID}`, parentDoc);\n                if ($panel.is(':visible')) {\n                    hidePanel();\n                }\n            }\n        });\n        // 条目和世界书操作按钮事件\n        $(parentDoc).on('click', '.wio-item-header, .wio-global-book-header', handleHeaderClick);\n        $(parentDoc).on('click', '.wio-item-toggle, .wio-global-toggle', handleToggleState);\n        $(parentDoc).on('click', '.wio-rename-btn', handleRename);\n        $(parentDoc).on('click', '.wio-rename-save-btn', handleConfirmRename);\n        $(parentDoc).on('click', '.wio-rename-cancel-btn', handleCancelRename);\n        $(parentDoc).on('keydown', '.wio-rename-input', handleRenameKeydown);\n        $(parentDoc).on('click', '.wio-delete-entry-btn', handleDeleteEntry);\n        $(parentDoc).on('click', '.wio-delete-book-btn', handleDeleteBook);\n        $(parentDoc).on('click', '.wio-rename-book-btn', handleRenameBook);\n        $(parentDoc).on('click', '.wio-create-entry-btn', handleCreateEntry);\n        $(parentDoc).on('click', '.wio-batch-recursion-btn', handleBatchSetRecursion);\n        $(parentDoc).on('click', '.wio-fix-keywords-btn', handleFixKeywords);\n        $(parentDoc).on('click', '.wio-edit-entries-btn', handleEditEntriesToggle);\n        console.log('[WorldInfoOptimizer] Event handlers bound successfully.');\n    };\n    // --- 初始化脚本 ---\n    console.log('[WorldInfoOptimizer] Starting initialization...');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/WorldInfoOptimizer/index.ts\n\n}");
  }
};

var __webpack_exports__ = {};

__webpack_modules__["./src/WorldInfoOptimizer/index.ts"]();